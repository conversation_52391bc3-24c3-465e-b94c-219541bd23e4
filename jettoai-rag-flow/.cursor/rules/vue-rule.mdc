---
description: 
globs: 
alwaysApply: false
---
---
description: 
globs: 
alwaysApply: true
---
# 技术栈规范
- 框架：Vue 3.5 + Composition API
- UI库: Arco Design (已封装增强版组件)
- 状态管理: Pinia
- 路由: vue-router 4.3+
- 请求库: axios (已封装在 `@/utils/http`)
- 样式框架：TailwindCss (简化css编写)

# 编译及构建工具
- 使用pnpm作为依赖包管理工具
- 使用Vite作为Vue的编译/构建工具

# 目录结构
- 根目录 `/` 包含项目的基本结构
- `/config/plugins`: vite插件相关配置
- `/node_modules`: 项目依赖模块
- `/public`: 包括公共文件，如 `favicon.ico` 

## 业务目录
- `/src/apis`: api接口
- `/src/assets`: 存放静态资源

- `/src/components`: 基于arco.design封装的通用业务组件 (生成代码时必须优先使用封装的组件，会在下面声明组件说明)
- `/src/config`: 项目配置文件，如全局设置
- `/src/constant`: 常量文件
- `/src/directives`: vue自定义指令 (可能会用到 `@/directives/permission` 自定义权限校验)
- `/src/layout`: 应用布局组件 (在路由配置时，默认使用路径 `@/layout/index.vue`)
- `/src/router`: 路由配置文件目录(包含了路由校验等文件，业务路由配置文件为`route.ts`)
- `/stores`: Pinia 状态管理设置目录 (不同的业务store需在`@/stores/modules`下创建，并由`@/stores/index.ts`导出)
- `/src/styles`: 全局样式及样式变量配置，包括 `tailwind.css、arco.design组件样式重置等`
- `/types`: 类型声明目录，用于声明全局自定义类型（如 `router.d.ts、api.d.ts`等）
- `/utils`: 工具类目录 (需要自行解析该目录下所有文件，在需要时优先引入该类)
- `/views`: 页面文件夹，包括登录页面、其他业务页面等 (不同业务模块在 `@/views`下创建目录管理)
- `App.vue`: 根组件
- `main.ts`: 项目入口文件

## 配置文件
- `.eslintrc.js`: 用于代码质量的 ESLint 配置
- `.gitignore`: Git 忽略文件列表
- `package.json`: 项目配置和依赖配置文件
- `pnpm.lock`: pnpm 锁文件，用于锁定依赖包版本
- `tailwind.config.js`: 配置 Tailwind CSS
- `vite.config.ts`: 配置 Vite 项目构建工具

# 项目最佳实践
- 遵循模块化和功能性的代码结构
- 利用TypeScript增强代码的可读性和安全性
- 实现响应式设计并采用移动优先策略
- 在适当的地方使用动态加载以优化性能
- 确保所有开发组件具有响应式支持，并为主要页面加载性能进行优化


# 注释说明
1. 为公共组件使用提供说明
2. 为组件中的重要函数或类添加说明
3. 为复杂的业务逻辑处理添加说明
4. 特殊情况的代码处理说明，包括特殊用途的变量、临界值、hack、算法等
5. 多重 `if` 判断语句需添加注释
6. 注释块必须以 `/**（至少两个星号）开头**/` 
7. 单行注释使用 `//` 

# 优先级规范

## 1. 通用业务组件优先级 (`@/components`，自行解析该目录下所有组件，在使用时所有组件不需要 import 引入)
1. AiCodeMirror - 代码编辑 
- 基于CodeMirror统一封装，支持代码编辑及预览

2. AiForm - 标准表单组件
- 统一表单布局和验证
- 处理表单提交和重置
- 支持配置项生成表单元素

3. AiPageLayout - 页面布局容器组件
- 统一页面容器
- 支持自定义元素布局，包含多个slot

4. AiSvgIcon - 图标组件
- 统一arco图标展示样式
- 支持svg

5. AiTable - 通用表格组件
- 基于a-table统一封装
- 支持自定义工具栏内容

6. AiFileUpload - 文件上传组件
- 统一文件上传交互
- 支持上传文件
- 支持删除文件功能

7. AiIconSelector - 图标选择器组件
- 统一图标选择交互
- 支持多种图标库
- 简化图标选择调用

## 2. 组合式API优先级 (`@/hooks/modules`)
1. useLoading - 加载状态管理
- 统一管理加载状态
- 提供加载文本控制
- 简化异步操作的加载状态处理


## 3. 工具函数优先级 (`@/utils`, 自行解析该目录下所有工具函数)
1. http - axios请求工具(`@/utils/http`)
- 统一请求配置和错误处理
- 支持请求拦截和响应拦截
- 集成认证token管理


## 使用优先级规则
1. 组件使用优先级：
- 第一优先级：使用项目通用业务组件(`@/components`)
- 第二优先级：使用ArcoDesign组件库
- 第三优先级：创建新的组件

2. 组合式API使用优先级：
- 第一优先级：使用项目自定义组合式API(`@/hooks/modules`)
- 第二优先级：使用Vue核心组合式API
- 第三优先级：创建新的组合式API

3. 工具函数使用优先级：
- 第一优先级：使用项目自定义工具函数(`@/utils`)
- 第二优先级：使用第三方工具库
- 第三优先级：创建新的工具函数

4. 请求处理规范：
- 统一使用 @/utils/http 中的请求方法
- API请求必须在@/apis下创建，不同业务api文件创建不同的`api`文件, 并由`@/apis/index`导出

5. 状态管理规范：
- 组件内部状态优先使用 ref/reactive
- 跨组件状态优先使用 Pinia store
- 临时全局状态可使用 provide/inject

6. 类型定义规范：
- 优先使用项目定义的类型(`@/types`)
- 其次扩展第三方库的类型
- 最后定义新的类型

# 代码生成规则
- 在引入文件时，需要使用全文件路径，不能省略 `index.*`
- 在使用通用业务组件时，不需要引入文件
- 生成应对的前端代码文件存放在 `src/views` 目录下，优先使用封装的通用业务组件
- 在 `src/router/route.ts` 内配置相应的路由,参考该文件下 `/template` 路由配置规则，注意hidden不要设置为true
- 在 `src/apis` 目录下生成相应的api文件, 参考`src/apis/template/index.ts`
- 在 `src/mock` 目录下生成相应的模拟请求文件，参考 `src/mock/template/index.ts`
- 列表页面请参考: `src/template/index.vue`，列表AiTable要结合useTable一起使用(useTable方法引用路径：src/hooks/modules/useTable.ts)
- 新增弹窗功能参考: `src/template/AppAddModal.vue`
- 详情抽屉功能参考: `src/template/AppDetailDrawer.vue`
- 通用tabs布局页面参考: `src/template/CommonTabs.vue`
- 更多业务代码可参考工程 `src/views` 下其他代码文件

# 基于Figma UI设计图生成规则
- 严格按照UI设计图中的字段展示
- 严格按照UI设计图中的功能按钮展示


