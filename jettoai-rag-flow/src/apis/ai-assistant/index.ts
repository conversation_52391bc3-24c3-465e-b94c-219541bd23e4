// import type * as T from './type'
import http from '@/utils/http'
// export type * from './type'

const BASE_URL = '/ai-api'

export function getTaskList(params?: any) {
  return http.get(`${BASE_URL}/tasks`, params)
}
// export function getAppsLists() {
//   return http.get<T.AppsListResponse>(`${BASE_URL}/explore/apps`)
// }

/**
 * 获取助手列表
 * @param params 查询参数
 * @returns 助手列表数据
 */
export function getAssistantList(params?: any) {
  return http.get('/api/ai-assistant/list', { params })
}

/**
 * 创建助手
 * @param data 助手数据
 * @returns 创建结果
 */
export function createAssistant(data: any) {
  return http.post('/api/ai-assistant/create', data)
}

/**
 * 获取任务列表
 * @param params 查询参数
 * @returns 任务列表数据
 */
// export function getTaskList(params?: any) {
//   return http.get('/api/ai-assistant/task/list', { params })
// }

/**
 * 创建任务
 * @param data 任务数据
 * @returns 创建结果
 */
export function createTask(data: any) {
  return http.post('/api/ai-assistant/task/create', data)
}

/**
 * 获取任务详情
 * @param taskId 任务ID
 * @returns 任务详情数据
 */
export function getTaskDetail(taskId: string) {
  return http.get(`/api/ai-assistant/task/${taskId}`)
}

/**
 * 终止任务
 * @param taskId 任务ID
 * @returns 操作结果
 */
export function terminateTask(taskId: string) {
  return http.post(`/api/ai-assistant/task/${taskId}/terminate`)
}

/**
 * 继续任务
 * @param taskId 任务ID
 * @returns 操作结果
 */
export function continueTask(taskId: string) {
  return http.post(`/api/ai-assistant/task/${taskId}/continue`)
}

/**
 * 删除任务
 * @param taskId 任务ID
 * @returns 操作结果
 */
export function deleteTask(taskId: string) {
  return http.del(`/api/ai-assistant/task/${taskId}`)
}

/**
 * 评价任务
 * @param taskId 任务ID
 * @param data 评价数据
 * @returns 操作结果
 */
export function evaluateTask(taskId: string, data: any) {
  return http.post(`/api/ai-assistant/task/${taskId}/evaluate`, data)
}
