// AI助手相关类型定义

/**
 * 助手信息
 */
export interface Assistant {
  id: string
  name: string
  description: string
  icon: string
  updateTime: string
  count: string
  type: string
}

/**
 * 阶段状态
 */
export type PhaseStatus = 'not_started' | 'processing' | 'succeeded' | 'failed' | 'partially_succeeded'

/**
 * 阶段信息
 */
export interface PhaseInfo {
  status?: PhaseStatus
  start_time?: string
  end_time?: string
  error?: string
  prompt?: string
  error_categories?: string[]
}

/**
 * 任务阶段
 */
export interface TaskPhases {
  phase1?: PhaseInfo // 要素提取
  phase2?: PhaseInfo // 正交结合
  phase3?: PhaseInfo // 案例生成
}

/**
 * 任务项
 */
export interface TaskItem {
  id: string
  name?: string
  assistantId?: string
  assistantName?: string
  taskName?: string
  icon?: string
  extractProgress?: number
  combineProgress?: number
  generateProgress?: number
  progress?: number
  status: string
  createTime?: string
  created_at?: string
  executeTime?: string
  completeTime?: string
  evaluation?: string
  assessment?: string
  phases?: TaskPhases
  split_level?: number
  sys_names?: string[] | string
  categories?: string[]
}

/**
 * 查询参数
 */
export interface QueryParams {
  name?: string
  start_time?: string
  end_time?: string
  skip?: number
  limit?: number
}

/**
 * 分页响应
 */
export interface PaginatedResponse<T> {
  items: T[]
  total: number
  skip: number
  limit: number
}

/**
 * API响应
 */
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

/**
 * 任务列表响应
 */
export type TaskListResponse = ApiResponse<PaginatedResponse<TaskItem>>

/**
 * 助手列表响应
 */
export type AssistantListResponse = ApiResponse<Assistant[]>

/**
 * 默认提示词响应
 */
export interface DefaultPromptResponse {
  prompt: string
}

/**
 * 重新生成参数
 */
export interface RegenerateParams {
  categories?: string[]
  regenerateAll?: boolean
}

/**
 * 创建任务表单数据
 */
export interface CreateTaskForm {
  file: File | null
  sys_names: string[]
  split_level: number
  prompt: string
}

/** 应用响应类型 */
export interface AppsListResponse {
  categories: string[]
  recommended_apps: string[]
}
