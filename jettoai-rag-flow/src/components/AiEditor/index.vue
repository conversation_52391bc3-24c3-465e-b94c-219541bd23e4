<template>
  <div class="ai-editor-container" :style="{ width: width + 'px', height: height + 'px' }">
    <div
      ref="editorRef"
      class="ai-editor"
      contenteditable="true"
      @input="handleInput"
      @keydown="handleKeydown"
      @keyup="handleKeyup"
      @paste="handlePaste"
      @click="handleClick"
    />

    <!-- 自定义组件弹出层 -->
    <div
      v-if="showCustomComponent"
      class="custom-component-popup"
      :style="{ top: popupPosition.top + 'px', left: popupPosition.left + 'px' }"
    >
      <slot name="custom-component" />
      <div class="popup-close" @click="hideCustomComponent">×</div>
    </div>

    <!-- 字数统计 -->
    <div class="word-count">{{ textCount }} 字</div>

    <!-- 缩放控制器 -->
    <div class="resize-handle" @mousedown="startResize" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, nextTick, defineEmits, defineProps } from 'vue'

defineOptions({
  name: 'AiEditor'
})

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  width: {
    type: Number,
    default: 500
  },
  height: {
    type: Number,
    default: 300
  },
  triggerChar: {
    type: String,
    default: '#'
  }
})

const emit = defineEmits(['update:modelValue', 'trigger-custom-component'])

// 编辑器DOM引用
const editorRef = ref<HTMLElement | null>(null)
// 文本计数
const textCount = ref(0)
// 自定义组件状态
const showCustomComponent = ref(false)
const popupPosition = reactive({
  top: 0,
  left: 0
})
// 缩放相关状态
const width = ref(props.width)
const height = ref(props.height)
const isResizing = ref(false)
const resizeStartPos = reactive({
  x: 0,
  y: 0
})
const initialSize = reactive({
  width: 0,
  height: 0
})

// 监听modelValue变化，更新编辑器内容
watch(
  () => props.modelValue,
  (newValue) => {
    if (editorRef.value && editorRef.value.innerHTML !== newValue) {
      editorRef.value.innerHTML = newValue
      updateTextCount()
    }
  },
  { immediate: true }
)

// 初始化编辑器
onMounted(() => {
  if (editorRef.value) {
    // 确保初始内容被包裹在p标签中
    if (!props.modelValue) {
      editorRef.value.innerHTML = '<p data-placeholder="1111"></p>'
    } else {
      // 如果有初始内容但不是以p标签开始，进行包装
      if (!props.modelValue.trim().startsWith('<p>')) {
        const paragraphs = props.modelValue.split('\n').map((line) => `<p>${line || '<br>'}</p>`)
        editorRef.value.innerHTML = paragraphs.join('')
      } else {
        editorRef.value.innerHTML = props.modelValue
      }
    }

    updateTextCount()
  }

  // 添加全局事件监听
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
})

// 处理输入事件
const handleInput = () => {
  if (!editorRef.value) return

  // 确保内容都被p标签包裹
  ensureParagraphWrapping()

  // 更新文本计数
  updateTextCount()

  // 更新v-model
  emit('update:modelValue', editorRef.value.innerHTML)
}

// 处理按键按下事件
const handleKeydown = (e: KeyboardEvent) => {
  // 处理左花括号自动闭合
  if (e.key === '{') {
    e.preventDefault()

    const selection = window.getSelection()
    if (!selection || selection.rangeCount === 0) return

    const range = selection.getRangeAt(0)

    // 保存当前位置的引用点
    const container = range.startContainer
    const parentNode = container.parentNode
    const offset = range.startOffset

    // 插入{}并获取插入的节点
    const textNode = document.createTextNode('{}')
    range.insertNode(textNode)

    // 创建新的范围，将光标定位在花括号中间
    const newRange = document.createRange()

    // 如果文本节点仍然在DOM中
    if (textNode.parentNode) {
      newRange.setStart(textNode, 1) // 设置在第一个字符之后，即 { 之后
      newRange.setEnd(textNode, 1) // 保持范围折叠
    } else {
      // 如果节点被规范化或其他DOM操作影响，尝试查找花括号
      findAndPositionCursor(parentNode, '{}')
    }

    // 应用新范围
    selection.removeAllRanges()
    selection.addRange(newRange)

    // 触发input事件以更新内容
    handleInput()
    return
  }

  // 处理回车键，确保新行被p标签包裹
  if (e.key === 'Enter') {
    // 仅当不是组合输入法状态时处理回车
    // 这可以防止在输入法组合状态下触发重复的回车操作
    if (!e.isComposing) {
      e.preventDefault()
      insertNewParagraph()
    }
    return
  }

  // 检查是否输入触发字符
  if (e.key === props.triggerChar) {
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      const rect = range.getBoundingClientRect()

      // 计算弹出位置
      if (editorRef.value) {
        const editorRect = editorRef.value.getBoundingClientRect()

        // 确保计算正确的位置，考虑到滚动和父容器位置
        popupPosition.top = Math.max(0, rect.bottom - editorRect.top)
        popupPosition.left = Math.max(0, rect.left - editorRect.left)

        // 延迟显示自定义组件，以便先完成字符的输入
        setTimeout(() => {
          showCustomComponent.value = true
          emit('trigger-custom-component')

          // 检查是否在视口内，如果不在则调整位置
          nextTick(() => {
            const popupElement = document.querySelector('.custom-component-popup') as HTMLElement
            if (popupElement) {
              const popupRect = popupElement.getBoundingClientRect()

              // 如果弹出框超出了编辑器底部，向上调整位置
              if (popupRect.bottom > editorRect.bottom) {
                popupPosition.top = Math.max(0, popupPosition.top - (popupRect.bottom - editorRect.bottom) - 10)
              }

              // 如果弹出框超出了编辑器右侧，向左调整位置
              if (popupRect.right > editorRect.right) {
                popupPosition.left = Math.max(0, popupPosition.left - (popupRect.right - editorRect.right) - 10)
              }
            }
          })
        }, 0)
      }
    }
    return
  }
}

// 处理按键松开事件
const handleKeyup = () => {
  // 可以在这里添加额外的按键处理逻辑
}

// 处理粘贴事件，确保粘贴的内容格式正确
const handlePaste = (e: ClipboardEvent) => {
  e.preventDefault()

  if (!e.clipboardData) return

  // 获取纯文本内容
  const text = e.clipboardData.getData('text/plain')

  // 将文本按行分割并包装在p标签中
  const paragraphs = text.split('\n').map((line) => line.trim())

  // 在光标位置插入处理后的内容
  const selection = window.getSelection()
  if (selection && selection.rangeCount > 0) {
    const range = selection.getRangeAt(0)
    range.deleteContents()

    // 插入每个段落
    paragraphs.forEach((paragraph, index) => {
      if (index > 0) {
        // 为除第一段外的段落创建新的p元素
        const p = document.createElement('p')
        p.textContent = paragraph || '\u200B' // 使用零宽空格确保空段落也能正常显示

        // 找到当前段落并在其后插入新段落
        const currentP = findParentParagraph(range.startContainer)
        if (currentP && currentP.parentNode) {
          currentP.parentNode.insertBefore(p, currentP.nextSibling)
        } else if (editorRef.value) {
          editorRef.value.appendChild(p)
        }
      } else {
        // 第一段直接插入当前位置
        const textNode = document.createTextNode(paragraph)
        range.insertNode(textNode)
        range.setStartAfter(textNode)
        range.collapse(true)
      }
    })

    // 更新编辑器状态
    handleInput()
  }
}

// 处理点击事件
const handleClick = () => {
  // 隐藏自定义组件
  hideCustomComponent()
}

// 隐藏自定义组件
const hideCustomComponent = () => {
  showCustomComponent.value = false
}

// 确保所有内容都被p标签包裹
const ensureParagraphWrapping = () => {
  if (!editorRef.value) return

  const childNodes = Array.from(editorRef.value.childNodes)

  childNodes.forEach((node) => {
    // 如果是文本节点，将其包裹在p标签中
    if (node.nodeType === Node.TEXT_NODE) {
      const p = document.createElement('p')
      p.appendChild(node.cloneNode())
      editorRef.value!.replaceChild(p, node)
    }
    // 如果是元素但不是p标签，也将其包裹在p标签中（除非是br）
    else if (node.nodeType === Node.ELEMENT_NODE && (node as Element).tagName.toLowerCase() !== 'p') {
      // 跳过处理span标签
      if ((node as Element).tagName.toLowerCase() === 'span') return

      const p = document.createElement('p')
      p.appendChild(node.cloneNode(true))
      editorRef.value!.replaceChild(p, node)
    }
  })

  // 如果没有内容，添加一个空的p标签
  if (editorRef.value.childNodes.length === 0) {
    const p = document.createElement('p')
    p.innerHTML = '<br>'
    editorRef.value.appendChild(p)
  }
}

// 查找文本并定位光标
const findAndPositionCursor = (node: Node | null, text: string, position: number = 1) => {
  if (!node) return false

  const selection = window.getSelection()
  if (!selection) return false

  // 如果是文本节点且包含目标文本
  if (node.nodeType === Node.TEXT_NODE && node.textContent && node.textContent.includes(text)) {
    const content = node.textContent
    const index = content.indexOf(text)

    if (index !== -1) {
      const range = document.createRange()
      range.setStart(node, index + position)
      range.collapse(true)
      selection.removeAllRanges()
      selection.addRange(range)
      return true
    }
  }

  // 递归搜索子节点
  if (node.hasChildNodes()) {
    for (let i = 0; i < node.childNodes.length; i++) {
      if (findAndPositionCursor(node.childNodes[i], text, position)) {
        return true
      }
    }
  }

  return false
}

// 在光标位置插入文本
const insertTextAtCursor = (text: string) => {
  const selection = window.getSelection()
  if (!selection || selection.rangeCount === 0) return

  const range = selection.getRangeAt(0)
  range.deleteContents()

  const textNode = document.createTextNode(text)
  range.insertNode(textNode)

  // 移动光标到插入的文本之后
  range.setStartAfter(textNode)
  range.collapse(true)
  selection.removeAllRanges()
  selection.addRange(range)

  // 触发input事件以更新内容
  handleInput()
}

// 插入新段落
const insertNewParagraph = () => {
  const selection = window.getSelection()
  if (!selection || selection.rangeCount === 0) return

  const range = selection.getRangeAt(0)

  // 找到当前所在的段落
  const currentP = findParentParagraph(range.startContainer)

  if (currentP) {
    // 保存当前选区信息
    const container = range.startContainer
    const offset = range.startOffset

    // 首先克隆当前段落，这样我们可以安全地修改它而不影响原始内容
    const tempP = currentP.cloneNode(true) as HTMLElement

    // 获取当前段落的HTML内容
    const fullHtml = currentP.innerHTML

    // 创建一个临时容器
    const temp = document.createElement('div')
    temp.innerHTML = fullHtml

    // 如果光标在文本节点内，我们需要找到等价的节点在临时容器中的位置
    if (container.nodeType === Node.TEXT_NODE) {
      // 构建一个从当前节点到段落根节点的路径
      const path = buildNodePath(container, currentP)

      // 根据路径找到临时容器中的对应节点
      const tempNode = findNodeByPath(temp, path)

      if (tempNode && tempNode.nodeType === Node.TEXT_NODE) {
        // 创建两个片段：前半部分和后半部分
        const beforeContent = fullHtml.substring(0, getNodeOffset(currentP, container, offset))
        const afterContent = fullHtml.substring(getNodeOffset(currentP, container, offset))

        // 更新当前段落只包含前半部分
        currentP.innerHTML = beforeContent || '<br>'

        // 创建新段落包含后半部分
        const newP = document.createElement('p')
        newP.innerHTML = afterContent || '<br>'

        // 在当前段落后插入新段落
        if (currentP.nextSibling) {
          currentP.parentNode?.insertBefore(newP, currentP.nextSibling)
        } else {
          currentP.parentNode?.appendChild(newP)
        }

        // 将光标移到新段落的开始位置
        const newRange = document.createRange()
        newRange.setStart(newP, 0)
        newRange.collapse(true)
        selection.removeAllRanges()
        selection.addRange(newRange)

        // 触发内容更新
        handleInput()
        return
      }
    }

    // 如果无法精确定位或其他情况，使用传统方法
    // 这是一个备用方案，确保即使复杂情况下也能正常工作
    const beforeText = currentP.innerHTML.substring(0, getOffsetInParent(currentP, container, offset))
    const afterText = currentP.innerHTML.substring(getOffsetInParent(currentP, container, offset))

    // 更新当前段落
    currentP.innerHTML = beforeText || '<br>'

    // 创建新段落
    const newP = document.createElement('p')
    newP.innerHTML = afterText || '<br>'

    // 在当前段落后插入新段落
    if (currentP.nextSibling) {
      currentP.parentNode?.insertBefore(newP, currentP.nextSibling)
    } else {
      currentP.parentNode?.appendChild(newP)
    }

    // 将光标移到新段落的开始位置
    const newRange = document.createRange()
    newRange.setStart(newP, 0)
    newRange.collapse(true)
    selection.removeAllRanges()
    selection.addRange(newRange)

    // 触发内容更新
    handleInput()
  }
}

// 构建从节点到其祖先的路径
const buildNodePath = (node: Node, ancestor: Node): number[] => {
  const path = []
  let current = node

  while (current !== ancestor && current.parentNode) {
    const parent = current.parentNode
    let index = 0
    let child = parent.firstChild

    while (child !== current && child) {
      index++
      child = child.nextSibling
    }

    path.unshift(index)
    current = parent
  }

  return path
}

// 根据路径在给定节点中查找目标节点
const findNodeByPath = (root: Node, path: number[]): Node | null => {
  let current = root

  for (let i = 0; i < path.length; i++) {
    if (!current.childNodes || current.childNodes.length <= path[i]) {
      return null
    }
    current = current.childNodes[path[i]]
  }

  return current
}

// 计算节点在父段落中的绝对偏移量
const getNodeOffset = (root: Node, node: Node, offset: number): number => {
  let totalOffset = 0

  // 使用TreeWalker遍历所有文本节点
  const walker = document.createTreeWalker(root, NodeFilter.SHOW_TEXT, null)
  let currentNode

  while ((currentNode = walker.nextNode())) {
    if (currentNode === node) {
      return totalOffset + offset
    }
    totalOffset += currentNode.textContent?.length || 0
  }

  return offset
}

// 获取节点在父元素中的偏移量
const getOffsetInParent = (parent: Node, node: Node, offset: number): number => {
  // 如果节点是段落本身
  if (node === parent) {
    return offset
  }

  // 计算HTML中的偏移量
  let result = 0

  // 使用TreeWalker遍历所有节点
  const walker = document.createTreeWalker(parent, NodeFilter.SHOW_ALL, null)
  let currentNode = walker.currentNode

  // 找到目标节点前的所有内容长度
  while (currentNode && currentNode !== node) {
    if (currentNode.nodeType === Node.TEXT_NODE) {
      result += currentNode.textContent?.length || 0
    }
    currentNode = walker.nextNode() as Node
  }

  // 如果找到了目标节点，加上偏移量
  if (currentNode === node) {
    result += offset
  }

  return result
}

// 查找父段落元素
const findParentParagraph = (node: Node): HTMLParagraphElement | null => {
  let current: Node | null = node

  while (current && current !== editorRef.value) {
    if (current.nodeType === Node.ELEMENT_NODE && (current as Element).tagName.toLowerCase() === 'p') {
      return current as HTMLParagraphElement
    }
    current = current.parentNode
  }

  return null
}

// 更新文本计数
const updateTextCount = () => {
  if (!editorRef.value) return

  // 获取纯文本内容并计算字数
  const text = editorRef.value.textContent || ''
  textCount.value = text.length
}

// 开始缩放
const startResize = (e: MouseEvent) => {
  e.preventDefault()
  isResizing.value = true

  resizeStartPos.x = e.clientX
  resizeStartPos.y = e.clientY

  initialSize.width = width.value
  initialSize.height = height.value
}

// 处理鼠标移动（用于缩放）
const handleMouseMove = (e: MouseEvent) => {
  if (!isResizing.value) return

  const deltaX = e.clientX - resizeStartPos.x
  const deltaY = e.clientY - resizeStartPos.y

  width.value = Math.max(200, initialSize.width + deltaX)
  height.value = Math.max(100, initialSize.height + deltaY)
}

// 处理鼠标松开（结束缩放）
const handleMouseUp = () => {
  isResizing.value = false
}
</script>

<style scoped lang="scss">
.ai-editor-container {
  position: relative;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;

  .ai-editor {
    width: 100%;
    height: calc(100% - 30px);
    padding: 10px;
    overflow-y: auto;
    outline: none;
    font-size: 14px;
    line-height: 1.5;
    :first-child {
      &:empty::before {
        content: attr(data-placeholder);
        color: var(--color-text-3);
        pointer-events: none;
      }
    }

    &:focus {
      border-color: #409eff;
    }

    p {
      margin: 0 0 8px 0;
      min-height: 21px;
    }
  }

  .word-count {
    position: absolute;
    bottom: 5px;
    right: 10px;
    font-size: 12px;
    color: #909399;
  }

  .resize-handle {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 10px;
    height: 10px;
    cursor: nwse-resize;
    background: linear-gradient(135deg, transparent 50%, #409eff 50%);
  }

  .custom-component-popup {
    position: absolute;
    background-color: white;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 10px;
    z-index: 100;
    min-width: 200px;

    .popup-close {
      position: absolute;
      top: 5px;
      right: 5px;
      width: 16px;
      height: 16px;
      line-height: 16px;
      text-align: center;
      cursor: pointer;
      color: #909399;

      &:hover {
        color: #409eff;
      }
    }
  }
}
</style>
