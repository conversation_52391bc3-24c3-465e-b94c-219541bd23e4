<template>
  <div class="config-var">
    <Panel class="mt-2">
      <template #title>
        <div class="flex items-center">
          <div class="mr-1">变量</div>
          <a-tooltip content="变量将以表单形式让用户在对话前填写，用户填写的表单内容将自动替换提示词中的变量。">
            <icon-question-circle />
          </a-tooltip>
        </div>
      </template>
      <template #headerRight>
        <a-popover trigger="click">
          <a-button size="small">添加</a-button>
          <template #content>
            <div class="min-w-[192px] rounded-lg">
              <div class="p-0">
                <div
                  v-for="option in optionList"
                  :key="option.value"
                  class="flex h-8 cursor-pointer items-center rounded-lg px-3 hover:bg-gray-50"
                  @click="handleSelectType(option.type)"
                >
                  <div class="ml-2 truncate text-sm text-gray-600">
                    {{ option.label }}
                  </div>
                </div>
                <div class="h-[1px] bg-gray-100 my-1" />
                <div
                  class="flex h-8 cursor-pointer items-center rounded-lg px-3 hover:bg-gray-50"
                  @click="handleSelectType('api')"
                >
                  <div class="ml-2 truncate text-sm text-gray-600">基于 API 的变量</div>
                </div>
              </div>
            </div>
          </template>
        </a-popover>
      </template>
      <template #default>
        <div class="mt-3 px-3 pb-3">
          <div
            v-for="(item, index) in varList"
            :key="index"
            class="bg-background-default group relative mb-1 flex h-[34px] w-full items-center rounded-lg border-[0.5px] border-components-panel-border-subtle bg-components-panel-on-panel-item-bg pl-2.5 pr-3 shadow-xs last-of-type:mb-0 hover:bg-components-panel-on-panel-item-bg-hover hover:shadow-sm"
          >
            <span class="mr-1 text-text-accent text-blue-500">{x}</span>
            <div class="flex w-0 grow items-center">
              <div class="system-sm-medium text-text-secondary">{{ item.name }}</div>
              <div class="system-xs-regular px-1 text-text-quaternary">.</div>
              <div class="system-xs-medium text-text-tertiary">{{ item.key }}</div>
            </div>
            <div class="shrink-0">
              <div class="flex items-center group-hover:hidden">
                <a-tag v-if="item.required">Required</a-tag>
                <span class="system-xs-regular pl-2 pr-1 text-text-tertiary">{{ item.type }}</span>
              </div>
              <div class="hidden items-center justify-end rounded-lg group-hover:flex">
                <div
                  class="mr-1 flex h-6 w-6 cursor-pointer items-center justify-center rounded-md hover:bg-black/5"
                  @click="handleEdit(item)"
                >
                  <icon-edit />
                </div>
                <div
                  class="mr-1 flex h-6 w-6 cursor-pointer items-center justify-center rounded-md hover:bg-black/5"
                  @click="handleDelete(index)"
                  @mouseenter="handleEnter"
                  @mouseleave="handleLeave"
                >
                  <icon-delete />
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </Panel>

    <a-modal title="编辑变量" :visible="addOrEditConfig.isShow" @ok="handleOk" @cancel="handleCancel">
      <ConfigModal v-if="addOrEditConfig.isShow" ref="configModalRef" :editItem="addOrEditConfig.item" />
    </a-modal>
  </div>
</template>
<script setup lang="ts">
import Panel from '@/views/app/configuration/base/feature-panel/index.vue'
import { InputVarType } from '@/views/app/workflow/types/workflow'
import ConfigModal from './config-modal/index.vue'
import { OPTION_LIST } from '@/views/app/workflow/constant/configuration'
import { getNewVar } from '@/views/app/workflow/utils/variable'

const props = withDefaults(
  defineProps<{
    promptVariables?: any[]
  }>(),
  {
    promptVariables: () => []
    // modelConfig: () => ({}) as any
  }
)
const varList = computed(() => {
  return props.promptVariables
})
const emits = defineEmits(['onPromptVariablesChange'])
const optionList = OPTION_LIST
const handleSelectType = (type) => {
  if (type === 'api') {
    // TODO-todo:xxx
    return false
  }
  const newVar = getNewVar('', type)
  const newPromptVariables = [...toRaw(varList.value), newVar]
  // props.modelConfig.configs.prompt_variables.push(newVar)
  // emits('onPromptVariablesChange', newVar)
  emits('onPromptVariablesChange', newPromptVariables)
}

const isDeleting = ref(false)
const isAddOrEdit = ref(false)
const addOrEditConfig = reactive({
  type: '',
  isShow: false,
  item: {}
})
const handleEdit = (item) => {
  console.log('edit-item:', item)
  addOrEditConfig.type = 'edit'
  addOrEditConfig.isShow = true
  addOrEditConfig.item = Object.assign(item, {
    label: item.name,
    variable: item.key,
    type: item.type === 'string' ? InputVarType.textInput : item.type
  })
}

const configModalRef = ref()
const handleOk = () => {
  const newForm = configModalRef.value.form
  console.log('11:', newForm)

  Object.assign(addOrEditConfig.item, {
    ...newForm,
    // updatePromptVariableItem  这个字段在组件中转来转去的。源码太费劲了。。。
    type: newForm.type === InputVarType.textInput ? 'string' : newForm.type
  })
  if (newForm.type !== InputVarType.select) {
    delete addOrEditConfig.item['options']
  }
  console.log('varList:', varList)
  addOrEditConfig.isShow = false
}
const handleCancel = () => {
  addOrEditConfig.isShow = false
}
const handleDelete = (index) => {
  const newPromptVariables = [...toRaw(varList.value)].filter((_, k) => k !== index)
  emits('onPromptVariablesChange', newPromptVariables)
}
const handleEnter = () => {
  isDeleting.value = true
}
const handleLeave = () => {
  isDeleting.value = false
}
watch(
  () => props.promptVariables,
  () => {
    console.log('watch:', props.promptVariables)
  }
)
</script>
<style scoped lang="scss">
.bg-background-default {
  background: #ffffff;
}
</style>
