<template>
  <div>
    <FeaturePanel class="mt-2">
      <template #title>
        <div>知识库</div>
      </template>
      <template #headerRight>
        <a-button size="small" @click="handleAddDataset">添加</a-button>
      </template>
      <template #default>
        <div class="ml-2 my-4 px-4">
          <div
            v-for="(item, index) in dataSetsList"
            :key="item.id"
            class="dataset-item group relative mb-1 flex h-10 w-full cursor-pointer items-center justify-between rounded-lg border-[0.5px] border-components-panel-border-subtle bg-components-panel-on-panel-item-bg px-2 last-of-type:mb-0 hover:bg-components-panel-on-panel-item-bg-hover"
          >
            <div class="flex w-0 grow items-center space-x-1.5">
              <div
                class="mr-2 flex h-6 w-6 shrink-0 items-center justify-center rounded-md border-[0.5px] border-[#EAECF5]">
                <icon-folder />
              </div>
              <div class="system-sm-medium w-0 grow truncate text-text-secondary">{{ item.name }}</div>
            </div>

            <div class="ml-2 hidden shrink-0 items-center space-x-1 group-hover:flex">
              <div
                class="mr-1 flex h-6 w-6 cursor-pointer items-center justify-center rounded-md hover:bg-black/5"
                @click="handleEdit(item)"
              >
                <icon-edit />
              </div>
              <div
                class="mr-1 flex h-6 w-6 cursor-pointer items-center justify-center rounded-md hover:bg-black/5"
                @click="handleDelete(index)"
              >
                <icon-delete />
              </div>
            </div>
            <div>
              <a-tag v-if="item.indexing_technique" class="shrink-0 group-hover:hidden" size="small">
                {{
                  formatIndexingTechniqueAndMethod(item.indexing_technique, item.retrieval_model_dict?.search_method)
                }}
              </a-tag>
              <a-tag v-if="item.provider === 'external'" class="shrink-0 group-hover:hidden" size="small">外部</a-tag>
            </div>
          </div>
        </div>

        <FeaturePanel class="mt-2">
          <template #title>
            <div class="h2 system-sm-semibold-uppercase text-text-secondary">
              元数据过滤
              <a-tooltip
                content="元数据过滤是使用元数据属性（例如标签、类别或访问权限）来细化和控制系统内相关信息的检索过程。"
              >
                <icon-question-circle />
              </a-tooltip>
            </div>
          </template>
          <template #headerRight>
            <div class="flex items-center">
              <a-dropdown :popup-max-height="false" @select="handleSelect">
                <a-button>
                  {{ modeName }}
                  <icon-down />
                </a-button>
                <template #content>
                  <a-doption v-for="option in optionList" :key="option.key" :value="option.key">
                    <div class="py-[10px]">
                      <div class="system-sm-semibold text-text-secondary leading-loose">{{ option.value }}</div>
                      <div class="system-xs-regular text-text-tertiary leading-normal">{{ option.desc }}</div>
                    </div>
                  </a-doption>
                </template>
              </a-dropdown>

              <div v-if="metadataFilterMode === MetadataFilteringModeEnum.manual" class="ml-1">
                <a-button>
                  <icon-align-center />
                  条件
                  <span
                    class="system-2xs-medium-uppercase ml-1 flex items-center rounded-[5px] border border-divider-deep px-1 text-text-tertiary"
                  >
                {{ datasetConfigs?.metadata_filtering_conditions?.conditions?.length || 0 }}
              </span>
                </a-button>
              </div>
            </div>
          </template>

          <template #default>
            <div v-if="metadataFilterMode === MetadataFilteringModeEnum.automatic" class="body-xs-regular px-4 text-text-tertiary">
              根据 Query Variable 自动生成元数据过滤条件
            </div>
          </template>
        </FeaturePanel>
      </template>
    </FeaturePanel>


  </div>
</template>
<script setup lang="ts">
import FeaturePanel from '@/views/app/configuration/base/feature-panel/index.vue'
import { formatIndexingTechniqueAndMethod, METADATA_SELECT } from '@/views/app/workflow/utils/configuration'
import { MetadataFilteringModeEnum } from '@/views/app/workflow/types/node'

const props = withDefaults(
  defineProps<{
    dataSets?: any[]
    datasetConfigs?: any
  }>(),
  {
    dataSets: () => [],
    datasetConfigs: () => ({})
  }
)
console.log('dataset', props, props.dataSets)
const dataSetsList = computed(() => {
  return props.dataSets
})
const emits = defineEmits(['addDataset'])
const metadataFilterMode = ref(props.datasetConfigs.metadata_filtering_mode)
const handleAddDataset = () => {
  emits('addDataset', 'add')
}

const handleEdit = (item) => {
}
const handleDelete = (index) => {
}

const optionList = METADATA_SELECT
const modeName = computed(() => {
  if (!metadataFilterMode.value) return ''
  const { value: name } = METADATA_SELECT.find((item) => item.key === metadataFilterMode.value)
  return name || ''
})
const handleSelect = (v) => {
  console.log(v)
  metadataFilterMode.value = v
}
watch(
  () => props.datasetConfigs,
  () => {
    metadataFilterMode.value = props.datasetConfigs.metadata_filtering_mode
  }
)
</script>

<style scoped lang="scss">
.dataset-item {
  background: #ffffff;

  &:hover {
    background: #f9fafb;
  }
}
</style>
