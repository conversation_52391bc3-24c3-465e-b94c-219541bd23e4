<template>
  <div class="flex flex-col code pb-1">
    <div class="code-header flex h-7 items-center justify-between pl-3 pr-2 pt-1">{{ props.title }}</div>
    <AiCodeMirror
      v-if="props.inputs"
      class="max-h-[300px] h-[calc(100%-30px)] overflow-hidden overflow-y-auto height: calc(100% - 30px);"
      :codeJson="props.inputs"
    />
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'CodeBlock'
})
const props = defineProps<{
  title: string
  inputs: string
}>()
</script>

<style scoped lang="scss">
.code {
  background-color: #c8ceda24;
  border: 1px solid #d0d5dc;
  border-radius: 5px;
  overflow-y: auto;

  &-header {
    font-size: 12px;
    color: #354052;
    font-weight: 600;
  }
}
</style>
