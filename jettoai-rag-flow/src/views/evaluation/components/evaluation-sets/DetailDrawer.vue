<template>
  <a-drawer v-model:visible="visible" title="评测集详情" :width="width >= 600 ? 600 : '100%'" :footer="false">
    <a-descriptions :column="2" size="large" class="general-description">
      <a-descriptions-item label="名称">{{ dataDetail?.name }}</a-descriptions-item>
      <a-descriptions-item label="类型">{{ dataDetail?.type === 2 ? '工作流类' : '对话类' }}</a-descriptions-item>
      <a-descriptions-item label="状态">
        <a-tag v-if="dataDetail?.status === 2" color="green">已发布</a-tag>
        <a-tag v-else color="orange">草稿</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ dataDetail?.createTime }}</a-descriptions-item>
      <a-descriptions-item label="输入文件">{{ dataDetail?.inputFileName || '-' }}</a-descriptions-item>
      <a-descriptions-item v-if="dataDetail?.type === 2" label="输出文件">
        {{ dataDetail?.outputFileName || '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="描述" :span="2">{{ dataDetail?.description || '-' }}</a-descriptions-item>
    </a-descriptions>
  </a-drawer>
</template>

<script setup lang="ts">
import { useWindowSize } from '@vueuse/core'
import { getEvaluationSetsDetail } from '@/apis/evaluation/evaluation-sets'
import { EvalDataset } from '@/apis/evaluation/evaluation-sets-type'

const { width } = useWindowSize()

const props = defineProps<{
  evaluationId: string
}>()

const dataDetail = ref<EvalDataset>()
const visible = ref(false)

// 查询详情
const getDataDetail = async () => {
  const { data } = await getEvaluationSetsDetail(props.evaluationId)
  dataDetail.value = data
}

// 打开
const onOpen = async (id: string) => {
  visible.value = true
  await getDataDetail()
}

defineExpose({ onOpen })
</script>
