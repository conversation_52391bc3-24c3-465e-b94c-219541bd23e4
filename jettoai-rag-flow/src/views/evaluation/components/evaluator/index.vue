<template>
  <div class="evaluator-tab">
    <AiTable
      row-key="id"
      :data="dataList || []"
      :columns="columns"
      :loading="loading"
      :scroll="{ x: '100%', y: '100%' }"
      :pagination="pagination"
      :disabled-tools="['size']"
      :disabled-column-keys="['name']"
      @refresh="search"
    >
      <template #toolbar-left>
        <a-space>
          <a-input-search v-model="queryForm.name" placeholder="搜索名称" allow-clear @search="search" />
          <a-button @click="reset">
            <template #icon><icon-refresh /></template>
            <template #default>重置</template>
          </a-button>
        </a-space>
      </template>
      <template #toolbar-right>
        <a-button type="primary" @click="onAdd">
          <template #icon><icon-plus /></template>
          <template #default>新建评估器</template>
        </a-button>
      </template>
      <template #action="{ record }">
        <a-space>
          <a-link title="详情" @click="onDetail(record)">详情</a-link>
          <a-link title="复制" @click="onCopy(record)">复制</a-link>
          <a-link status="danger" title="删除" @click="onDelete(record)">删除</a-link>
        </a-space>
      </template>
    </AiTable>
    <AddModal ref="AddModalRef" @save-success="search" />
    <DetailDrawer ref="DetailDrawerRef" />
  </div>
</template>

<script setup lang="ts">
import type { TableInstance } from '@arco-design/web-vue'
import { Message } from '@arco-design/web-vue'
import AddModal from './AddModal.vue'
import DetailDrawer from './DetailDrawer.vue'
import { getEvaluatorList, deleteEvaluator, copyEvaluator } from '@/apis/evaluation'
import { useTable } from '@/hooks'
import { isMobile } from '@/utils'

defineOptions({ name: 'EvaluatorTab' })

const queryForm = reactive<{
  name?: string
}>({})

const {
  tableData: dataList,
  loading,
  pagination,
  search,
  handleDelete
} = useTable<any>((page) => getEvaluatorList({ ...queryForm, ...page }), { immediate: true })

const columns: TableInstance['columns'] = [
  { title: '评估器名称', dataIndex: 'name', ellipsis: true, tooltip: true },
  { title: '最新版本', dataIndex: 'version', width: 100 },
  { title: '描述', dataIndex: 'description', ellipsis: true, tooltip: true },
  { title: '更新人', dataIndex: 'updateUserString', ellipsis: true, tooltip: true, width: 120 },
  { title: '更新时间', dataIndex: 'updateTime', width: 180 },
  {
    title: '操作',
    dataIndex: 'action',
    slotName: 'action',
    width: 160,
    align: 'left'
  }
]

// 重置
const reset = () => {
  queryForm.name = undefined
  search()
}

// 删除
const onDelete = (record) => {
  return handleDelete(() => deleteEvaluator(record.id), {
    content: '是否确定删除评估器「' + record.name + '」？',
    showModal: true
  })
}

// 复制
const onCopy = async (record) => {
  try {
    await copyEvaluator(record.id)
    Message.success('复制成功')
    search()
  } catch (error) {
    // 错误处理
  }
}

const AddModalRef = ref<InstanceType<typeof AddModal>>()
// 新增
const onAdd = () => {
  AddModalRef.value?.onAdd()
}

const DetailDrawerRef = ref<InstanceType<typeof DetailDrawer>>()
// 详情
const onDetail = (record) => {
  DetailDrawerRef.value?.onOpen(record.id)
}
</script>

<style scoped lang="scss">
.evaluator-tab {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
</style>
