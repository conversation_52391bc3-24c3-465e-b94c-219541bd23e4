<template>
  <a-drawer
    v-model:visible="visible"
    title="评测报告详情"
    :width="width >= 700 ? 700 : '100%'"
    :footer="false"
    body-class="drawer-markdown-body"
    body-style="padding: 16px 24px"
    @cancel="close"
  >
    <AiMarkdown :content="dataDetail.detailedMetricsText || ''" />
  </a-drawer>
</template>

<script setup lang="ts">
import { getReportDeatil } from '@/apis/evaluation/report'
import { EvalReport } from '@/apis/evaluation/report-type'
import { useWindowSize } from '@vueuse/core'

const props = defineProps<{
  detailVisible: boolean
  reportId: string | undefined
}>()

const emit = defineEmits<{
  (e: 'hide-drawer'): void
}>()

const { width } = useWindowSize()
const dataDetail = ref<EvalReport>({})
const visible = ref(props.detailVisible)

onMounted(async () => {
  if (props.reportId) {
    const { data } = await getReportDeatil(props.reportId)
    dataDetail.value = data || {}
  }
})

const close = () => {
  emit('hide-drawer')
}
</script>
<style scoped lang="scss">
.drawer-markdown-body {
  .markdown-body {
    padding: 0;
    :deep(pre) {
      overflow-x: auto;
    }
    :deep(table) {
      border-radius: 0;
    }
  }
}
</style>
