<template>
  <AiTable
    row-key="id"
    :data="dataList || []"
    :columns="columns"
    :loading="loading"
    :scroll="{ x: '100%', y: '100%' }"
    :pagination="pagination"
    @refresh="search"
  >
    <template #toolbar-left>
      <a-space>
        <a-input-search v-model="queryForm.taskName" placeholder="搜索名称/描述" allow-clear @search="search" />
        <a-select v-model="queryForm.taskType" placeholder="评测方式" allow-clear @change="search">
          <a-option :value="1">自动评测</a-option>
          <a-option :value="2">人工评测</a-option>
        </a-select>
        <a-select v-model="queryForm.status" placeholder="状态" allow-clear @change="search">
          <a-option v-for="item in taskStatus" :key="item.value" :value="item.value">{{ item.label }}</a-option>
        </a-select>
        <a-button @click="reset">
          <template #icon><icon-refresh /></template>
          <template #default>重置</template>
        </a-button>
      </a-space>
    </template>
    <template #toolbar-right>
      <a-dropdown :popup-max-height="false" @select="handleAdd">
        <a-button type="primary">
          <a-space>
            <icon-plus />
            创建
            <icon-down />
          </a-space>
        </a-button>
        <template #content>
          <a-doption :value="1">自动评测</a-doption>
          <a-doption :value="2">人工评测</a-doption>
        </template>
      </a-dropdown>
    </template>
    <template #taskType="{ record }">
      <span>{{ record.taskType === 1 ? '自动评测' : '人工评测' }}</span>
    </template>
    <template #evalTaskApp="{ record }">
      <span>{{ record?.evalTaskApp?.appName || '' }}</span>
    </template>
    <template #progress="{ record }">
      <a-progress :percent="Number((Number(record.progress || 0) / 100)?.toFixed(0))" size="small" />
    </template>
    <template #score="{ record }">
      <span>{{ record.score?.toFixed(2) || '-' }}</span>
    </template>
    <template #status="{ record }">
      <template v-for="item in taskStatus" :key="item.value">
        <a-tag v-if="record.status === item.value" :color="item.color">{{ item.label }}</a-tag>
      </template>
    </template>
    <template #action="{ record }">
      <a-space>
        <!-- <a-link title="详情" @click="onEdit(record)">编辑</a-link> -->
        <a-link v-if="record.status !== 1" title="启动" @click="onStartOrStop(record)">启动</a-link>
        <a-link v-if="record.status === 1" title="停止" @click="onStartOrStop(record)">终止</a-link>
        <a-link v-if="[0, 2, 3, 4].includes(record.status)" status="danger" title="删除" @click="onDelete(record)">
          删除
        </a-link>
      </a-space>
    </template>
  </AiTable>
</template>

<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import { useTable } from '@/hooks'
import { isMobile } from '@/utils'
import { getTaskPage, deleteTask, executeTask } from '@/apis/evaluation/task'
import { EvalTaskPageQuery, RIPageEvalTask } from '@/apis/evaluation/task-types'

defineOptions({ name: 'TaskTab' })

const router = useRouter()

const queryForm = reactive<EvalTaskPageQuery>({})

const taskStatus = [
  { value: 0, label: '待执行', color: 'blue' },
  { value: 1, label: '执行中', color: 'orange' },
  { value: 2, label: '已完成', color: 'green' },
  { value: 3, label: '失败', color: 'red' },
  { value: 4, label: '已终止', color: 'red' }
]

const {
  tableData: dataList,
  loading,
  pagination,
  search,
  handleDelete
} = useTable<RIPageEvalTask>((page) => getTaskPage({ model: queryForm, ...page }), {
  immediate: true
})

const columns = [
  {
    title: '任务名称',
    dataIndex: 'taskName',
    ellipsis: true,
    tooltip: true,
    fixed: !isMobile() ? 'left' : undefined,
    minWidth: 160
  },
  { title: '评测方式', dataIndex: 'taskType', slotName: 'taskType', width: 100 },
  { title: '评测对象', dataIndex: 'evalTaskApp', slotName: 'evalTaskApp', width: 200, ellipsis: true, tooltip: true },
  { title: '任务进展', dataIndex: 'progress', slotName: 'progress', width: 150 },
  // { title: '评分', dataIndex: 'score', slotName: 'score', width: 100 },
  { title: '状态', dataIndex: 'status', slotName: 'status', width: 100 },
  { title: '创建时间', dataIndex: 'createTime', width: 180 },
  {
    title: '操作',
    dataIndex: 'action',
    slotName: 'action',
    width: 120,
    align: 'left',
    fixed: !isMobile() ? 'right' : undefined
  }
]

// 重置
const reset = () => {
  queryForm.taskName = undefined
  queryForm.taskType = undefined
  queryForm.status = undefined
  search()
}

// 删除
const onDelete = (record) => {
  return handleDelete(() => deleteTask([record.id]), {
    content: '是否确定删除评测任务【' + record.taskName + '】？',
    showModal: true
  })
}

// 启动/终止
const onStartOrStop = async (record) => {
  try {
    const status = [0, 2, 3, 4].includes(record.status) ? 1 : 4
    const res = await executeTask(record.id, status)
    if (res.isSuccess) {
      Message.success('操作成功')
      search()
    }
  } catch (error) {
    // 错误处理
  }
}

// 创建
const handleAdd = (value) => {
  router.push({ path: '/evaluation/create-task', query: { taskType: value } })
}

// 详情
// const onDetail = (record: EvalTask) => {
//   router.push({ path: '/evaluation/create-task', query: { taskType: record.taskType, taskId: record.id } })
// }
</script>

<style scoped lang="scss">
:deep(.arco-table) {
  .arco-progress-line-text {
    margin-left: 0;
  }
}
</style>
