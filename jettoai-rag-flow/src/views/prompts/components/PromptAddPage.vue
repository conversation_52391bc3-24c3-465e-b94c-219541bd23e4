<template>
  <div class="prompt-container">
    <!-- 顶部标题区域 -->
    <div class="page-header">
      <div class="cursor-pointer" @click="router.go(-1)">
        <icon-left />
        <span class="pl-2">{{ isEdit ? '编辑提示词' : '新增提示词' }}</span>
      </div>
      <a-button type="primary" @click="handleSubmit">提 交</a-button>
    </div>
    <div class="prompt-container-layout">
      <div class="prompt-container-layout-wrapper">
        <div class="prompt-container-layout-info-wrapper">
          <div class="pb-4 font-[500]">基本信息</div>
          <AiForm ref="basicInfoformRef" v-model="layoutForm" :columns="layoutColumns" />
          <div class="prompt-container-layout-block-wrapper">
            <PromptBlock ref="promptBlock" />
          </div>
        </div>
        <div class="prompt-container-layout-model-wrapper">
          <PromptModel ref="promptModel" />
        </div>
      </div>
      <div class="prompt-container-layout-preview-wrapper">
        <PromptPreview />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import { saveUsingPost, updateUsingPut, getUsingGet } from '@/apis/prompts'
import type { ExtendedPromptDTO, Prompt, PromptSaveDTO, PromptUpdateDTO } from '@/apis/prompts/type'
import { type ColumnItem } from '@/components/AiForm'
import PromptBlock from './PromptBlock.vue'
import PromptModel from './PromptModel.vue'
import PromptPreview from './PromptPreview.vue'

defineOptions({ name: 'PromptAddPage' })

const router = useRouter()
const route = useRoute()

const promptId = computed(() => route.params.id as string | undefined)
const isEdit = computed(() => !!promptId.value)

const basicInfoformRef = ref()
const promptBlock = ref()
const promptModel = ref()

const layoutForm = ref<ExtendedPromptDTO>({
  name: '',
  promptKey: '',
  type: 'scene',
  content: '',
  tags: [],
  description: '',
  helpInfo: '',
  status: 1
})

const layoutColumns: ColumnItem[] = [
  {
    label: 'Prompt Key',
    field: 'promptKey',
    type: 'input',
    span: 12,
    rules: [{ required: true, message: '请输入Prompt Key' }]
  },
  {
    label: '类型',
    field: 'type',
    type: 'select',
    span: 12,
    required: true,
    props: {
      options: [
        { label: '场景提示词', value: 'scene' },
        { label: '提示词块', value: 'block' }
      ],
      multiple: false,
      allowClear: true
    }
  },
  {
    label: '名称',
    field: 'name',
    span: 24,
    type: 'input',
    required: true,
    props: {
      placeholder: '请输入提示词名称'
    }
  },
  {
    label: '描述',
    field: 'description',
    type: 'textarea',
    span: 24,
    props: {
      placeholder: '简要描述这个提示词的用途...',
      maxLength: 0,
      autoSize: {
        minRows: 4,
        maxRows: 6
      }
    }
  },
  {
    label: '标签',
    field: 'tags',
    type: 'input-tag',
    span: 24,
    props: {
      placeholder: '请输入标签，按回车添加'
    }
  },
  {
    label: 'Prompt 内容',
    field: 'content',
    type: 'textarea',
    span: 24,
    props: {
      placeholder: '输入你的提示词内容，支持使用{{VARIABLE NAME}}格式的变量',
      maxLength: 0,
      autoSize: {
        minRows: 8,
        maxRows: 10
      }
    },
    extra: '提示:使用 {{}}变量名称{{}}的格式来定义可替换的变量'
  }
]

// 提交表单
const handleSubmit = () => {
  console.log(promptBlock.value?.promptBlockData)
  console.log(promptModel.value?.modelForm)
  // formRef.value?.validate(async (errors) => {
  //   if (errors) {
  //     return
  //   }
  //   try {
  //     router.push('/prompts')
  //   } catch (error) {
  //     Message.error('保存失败')
  //   }
  // })
}

// 运行预览
const runPreview = () => {
  Message.success('预览请求已发送')
}
</script>

<style scoped lang="scss">
.prompt-container {
  height: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
  }
  .prompt-container-layout {
    flex: 1;
    height: 100%;
    display: flex;
    background-color: #f7f6fd;
    flex-direction: row;
    border-top: 1px solid #e9ebf2;
    border-bottom: 1px solid #e9ebf2;
    &-wrapper {
      flex: 1;
      // padding: 8px 16px;
      display: flex;
      flex-direction: row;
    }
    &-info-wrapper,
    &-model-wrapper,
    &-preview-wrapper {
      background-color: #fff;
      padding: var(--padding);
      height: 100%;
      overflow-y: auto;
    }
    &-info-wrapper {
      flex: 1;
      padding-bottom: 40px;
    }
    &-model-wrapper {
      width: 320px;
      border-left: 1px solid #e9ebf2;
    }
    &-block-wrapper {
      border-top: 1px solid #e9ebf2;
      padding: 20px 0;
    }
    &-preview-wrapper {
      width: 400px;
      border-left: 1px solid #e9ebf2;
      overflow: hidden;
    }
  }
}
</style>
