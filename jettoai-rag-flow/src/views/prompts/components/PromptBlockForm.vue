<template>
  <AiForm ref="blockFormRef" v-model="blockForm" :columns="blockColumns" />
</template>

<script setup lang="ts">
import type { ExtendedPromptDTO } from '@/apis/prompts/type'
import { type ColumnItem } from '@/components/AiForm'

defineOptions({ name: 'PromptBlockForm' })

const props = defineProps<{
  blockItemForm?: ExtendedPromptDTO
}>()
const blockFormRef = ref()

const blockForm = ref<ExtendedPromptDTO>(props.blockItemForm || {})

const blockColumns: ColumnItem[] = [
  {
    label: 'Prompt Key',
    field: 'promptKey',
    type: 'input',
    span: 12,
    rules: [{ required: true, message: '请输入Prompt Key' }]
  },
  {
    label: 'Prompt 名称',
    field: 'name',
    span: 12,
    type: 'input',
    required: true,
    props: {
      placeholder: '请输入提示词块名称'
    }
  },
  {
    label: '描述',
    field: 'description',
    type: 'input',
    span: 24,
    props: {
      placeholder: '简要描述这个提示词的用途...'
    }
  },
  {
    label: '标签',
    field: 'tags',
    type: 'input-tag',
    span: 24,
    props: {
      placeholder: '请输入标签，按回车添加'
    }
  },
  {
    label: 'Prompt 内容',
    field: 'content',
    type: 'textarea',
    span: 24,
    props: {
      placeholder: '输入你的提示词内容，支持使用{{VARIABLE NAME}}格式的变量',
      maxLength: 0,
      autoSize: {
        minRows: 2,
        maxRows: 4
      }
    }
  },
  {
    label: '帮助信息',
    field: 'helpInfo',
    type: 'input',
    span: 24,
    props: {
      placeholder: '添加使用说明或注意事项'
    }
  }
]
</script>

<style scoped lang="scss"></style>
