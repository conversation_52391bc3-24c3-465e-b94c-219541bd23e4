<template>
  <div class="assistant-carousel-container">
    <!-- Swiper 轮播容器 -->
    <div ref="swiperContainer" class="swiper-container">
      <div class="swiper-wrapper">
        <!-- 创建助手卡片 -->
        <div class="swiper-slide">
          <div
            class="create bg-[var(--color-fill-1)] border-[var(--color-border-2)] border-[1px] border-dashed rounded-lg p-4"
            @click="handleCreate"
          >
            <div class="create-content">
              <div class="create-icon">
                <icon-plus />
              </div>
              <div class="create-title">创建智能体</div>
              <div class="create-desc">选择应用模版，使用该模版创建应用到对应的空间</div>
            </div>
          </div>
        </div>

        <!-- 助手卡片 -->
        <div v-for="(assistant, index) in assistantList" :key="assistant.id || index" class="swiper-slide">
          <AssistantCard :assistant="assistant" />
        </div>
      </div>
    </div>

    <!-- 导航按钮 -->
    <div ref="prevButton" class="swiper-button-prev carousel-nav-btn carousel-nav-prev" />
    <div ref="nextButton" class="swiper-button-next carousel-nav-btn carousel-nav-next" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { Swiper } from 'swiper'
import { Navigation, Autoplay } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/navigation'
import AssistantCard from './AssistantCard.vue'

interface Assistant {
  id: string
  name: string
  description: string
  icon: string
  updateTime: string
  count: string
  version: string
  type: string
}

const props = defineProps({
  assistantList: {
    type: Array as () => Assistant[],
    default: () => []
  }
})

const emit = defineEmits(['create'])

// 模板引用
const swiperContainer = ref<HTMLElement>()
const prevButton = ref<HTMLElement>()
const nextButton = ref<HTMLElement>()

// Swiper 实例
let swiperInstance: Swiper | null = null

// 处理创建助手
const handleCreate = () => {
  emit('create')
}

// 初始化 Swiper
const initSwiper = () => {
  if (!swiperContainer.value) return

  swiperInstance = new Swiper(swiperContainer.value, {
    // 模块配置
    modules: [Navigation, Autoplay],

    // 基础配置
    slidesPerView: 4,
    spaceBetween: 24,
    loop: false,

    // 响应式断点
    breakpoints: {
      320: {
        slidesPerView: 1,
        spaceBetween: 16
      },
      640: {
        slidesPerView: 2,
        spaceBetween: 20
      },
      1024: {
        slidesPerView: 3,
        spaceBetween: 16
      },
      1280: {
        slidesPerView: 4,
        spaceBetween: 16
      },
      1680: {
        slidesPerView: 4,
        spaceBetween: 20
      }
    },

    // 导航配置
    navigation: {
      nextEl: nextButton.value,
      prevEl: prevButton.value
    },

    // 动画效果
    speed: 300,
    effect: 'slide',

    // 其他配置
    grabCursor: true,
    watchOverflow: true
  })
}

// 销毁 Swiper
const destroySwiper = () => {
  if (swiperInstance) {
    swiperInstance.destroy(true, true)
    swiperInstance = null
  }
}

// 监听助手列表变化，更新 Swiper
watch(
  () => props.assistantList,
  () => {
    if (swiperInstance) {
      // 延迟更新以确保 DOM 已更新
      setTimeout(() => {
        swiperInstance?.update()
      }, 100)
    }
  },
  { deep: true }
)

// 组件挂载
onMounted(() => {
  // 延迟初始化以确保 DOM 完全渲染
  setTimeout(() => {
    initSwiper()
  }, 100)
})

// 组件卸载
onUnmounted(() => {
  destroySwiper()
})
</script>

<style scoped>
.assistant-carousel-container {
  position: relative;
  width: 100%;
}

.swiper-container {
  width: 100%;
  padding: 0 30px; /* 为导航按钮留出空间 */
}

.swiper-slide {
  height: auto;
  display: flex;
}

.swiper-slide > * {
  width: 100%;
  height: 100%;
}

/* 导航按钮样式 */
.carousel-nav-btn {
  position: absolute;
  top: 50%;
  color: #666;
  &::after {
    font-size: 26px;
  }
}

.carousel-nav-btn:hover {
  background: #5147ff;
  color: #ffffff;
  border-color: #5147ff;
  border-radius: 5px;
  box-shadow: 0 4px 12px rgba(81, 71, 255, 0.3);
}

.carousel-nav-btn.swiper-button-disabled {
  opacity: 0.3;
  cursor: not-allowed;
  pointer-events: none;
}

.carousel-nav-prev {
  left: 0;
}

.carousel-nav-next {
  right: 0;
}

/* 创建助手卡片样式 */
.create {
  cursor: pointer;
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  height: 100%;
}

.create:hover {
  box-shadow: 0px 2px 8px 0px rgba(81, 71, 255, 0.2);
  border: 1px solid #aad4ff;
}

.create-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100%;
}

.create-icon {
  width: 33px;
  height: 33px;
  border-radius: 50%;
  background-color: var(--color-fill-2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  color: var(--color-text-1);
}

.create-icon:hover {
  background-color: var(--color-primary-light-4);
  color: var(--color-primary);
}

.create-title {
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 4px;
}

.create-desc {
  font-size: 13px;
  color: var(--color-text-3);
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 1279px) {
  .swiper-container {
    padding: 0 40px;
  }

  .carousel-nav-btn {
    width: 36px;
    height: 36px;
  }
}

@media (max-width: 1023px) {
  .swiper-container {
    padding: 0 35px;
  }

  .carousel-nav-btn {
    width: 32px;
    height: 32px;
  }
}

@media (max-width: 639px) {
  .swiper-container {
    padding: 0 30px;
  }

  .carousel-nav-btn {
    width: 28px;
    height: 28px;
  }
}
</style>
