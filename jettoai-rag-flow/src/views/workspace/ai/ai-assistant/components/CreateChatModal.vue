<template>
  <a-modal
    v-model:visible="visible"
    :title="title"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 600 ? 600 : '100%'"
    draggable
    @close="handleCancel"
  >
    <AiForm ref="formRef" v-model="form" :columns="columns">
      <template #file>
        <div class="w-full flex flex-col">
          <div>
            <AiFileUpload
              v-model="form.file"
              inputsKey="file"
              :tips="'请上传.docx 等格式文档'"
              :accept="'.docx'"
              @uploadFile="onUploadFile"
            />
          </div>
        </div>
      </template>
      <template #prompt>
        <div class="w-full flex flex-col">
          <md-editor v-model="form.prompt" style="height: 240px" :preview="false" :toolbars="toolbars" />
        </div>
      </template>
    </AiForm>
    <template #footer>
      <div class="flex justify-end">
        <a-button class="mr-2" @click="handleCancel">取消</a-button>
        <a-button type="primary" :loading="submitting" @click="handleSubmit">创建</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import { useWindowSize } from '@vueuse/core'
import { createTask, getDefaultPrompt } from '@/apis/ai-assistant'
import { type ColumnItem } from '@/components/AiForm'
import { useResetReactive } from '@/hooks'
import { MdEditor } from 'md-editor-v3'
import 'md-editor-v3/lib/style.css'
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '新建会话'
  },
  initialData: {
    type: Object,
    default: () => ({})
  },
  isRegenerate: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'create', // 'create' | 'regenerate'
    validator: (value: string) => ['create', 'regenerate'].includes(value)
  }
})
// 工具栏配置 - 修复类型问题
const toolbars = [
  'bold',
  'italic',
  'strikethrough',
  'heading',
  'quote',
  'list',
  'ordered-list',
  'unordered-list',
  'code',
  'code-block',
  'link',
  'image',
  'table',
  'preview',
  'fullscreen'
] as any
const emit = defineEmits(['update:modelValue', 'cancel', 'submit'])

// 窗口大小
const { width } = useWindowSize()

// 表单引用
const formRef = ref()

// 提交状态
const submitting = ref(false)

// 类型选项（暂时未使用）
// const typeOptions = [
//   { label: '测试用例生成助手', value: 'test-case' },
//   { label: '智能问答助手', value: 'qa' },
//   { label: '代码审查助手', value: 'code-review' },
//   { label: '文章写作助手', value: 'article' }
// ]
// 系统选项 - 配置变量
const availableSystems = ref(['系统1', '系统2', '系统3', '核心系统', '业务系统', '数据系统'])

// 表单数据
const [form, resetForm] = useResetReactive({
  file: null as File | null,
  sys_names: [],
  split_level: 0,
  prompt: ''
})

// 删除未使用的版本选项

// 表单列定义 - 根据模式动态生成
const columns = computed<ColumnItem[]>(() => {
  const baseColumns: ColumnItem[] = [
    {
      label: '分块级别',
      field: 'split_level',
      type: 'input-number',
      span: 24,
      required: true,
      props: {
        placeholder: '请输入分块级别'
      }
    },
    {
      label: '涉及系统',
      field: 'sys_names',
      type: 'select',
      span: 24,
      required: true,
      props: {
        placeholder: '请选择',
        options: availableSystems.value,
        multiple: true,
        allowCreate: true
      }
    },
    {
      label: '提示词',
      field: 'prompt',
      span: 24,
      required: true
    }
  ]

  // 只有创建模式才显示文件上传
  if (props.mode === 'create') {
    baseColumns.unshift({
      label: '上传文档',
      field: 'file',
      span: 24,
      rules: [{ required: true, message: '请选上传文档' }]
    })
  }

  return baseColumns
})

// 取消处理
const handleCancel = () => {
  emit('update:modelValue', false)
  emit('cancel')
  resetForm()
}
const onUploadFile = async (
  file: File,
  onProgress?: (percent: number) => void,
  _key?: string,
  _transfer_method?: string,
  callback?: (res: any) => void
) => {
  if (onProgress) {
    onProgress(100)
  }
  form.file = file
  if (callback) {
    callback(file)
  }
}
// 获取默认提示词
const loadDefaultPrompt = async () => {
  try {
    const response = await getDefaultPrompt('phase1')
    if (response && response.data) {
      form.prompt = response.data.prompt || ''
    }
  } catch (error) {
    console.error('加载默认提示词失败', error)
  }
}

// 提交处理
const handleSubmit = async () => {
  try {
    const isInvalid = await formRef.value?.formRef?.validate()
    if (isInvalid) return

    // 创建模式需要验证文件
    if (props.mode === 'create' && !form.file) {
      Message.error('请上传文档')
      return
    }

    submitting.value = true

    if (props.mode === 'create') {
      // 创建模式 - 创建新任务
      const formData = new FormData()
      formData.append('file', form.file!)
      formData.append('sys_names', JSON.stringify(form.sys_names))
      formData.append('split_level', form.split_level.toString())
      formData.append('prompt', form.prompt)

      await createTask(formData)
      Message.success('会话创建成功')
    } else {
      // 重新生成模式 - 直接返回表单数据
      Message.success('重新生成参数已设置')
    }
    emit('update:modelValue', false)
    emit('submit', { ...form })
    resetForm()
  } catch (error) {
    console.error(props.mode === 'create' ? '创建会话失败' : '设置参数失败', error)
    Message.error(props.mode === 'create' ? '创建会话失败，请稍后重试' : '设置参数失败，请稍后重试')
  } finally {
    submitting.value = false
  }
}

// 监听初始数据变化
const initializeForm = () => {
  if (props.initialData && Object.keys(props.initialData).length > 0) {
    form.split_level = props.initialData.splitLevel || 0
    form.sys_names = props.initialData.sysNames || []
    form.prompt = props.initialData.prompt || ''
  }
}

// 组件挂载时加载默认提示词
onMounted(() => {
  if (!props.isRegenerate) {
    loadDefaultPrompt()
  }
  initializeForm()
})

// 监听弹窗显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听初始数据变化
watch(
  () => props.initialData,
  () => {
    initializeForm()
  },
  { deep: true }
)
</script>

<style scoped>
.analysis-container {
  border: 1px solid #f0f0f0;
}

.template-bg {
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.76), rgba(255, 255, 255, 0.8));
  border: 1px solid #d9d9d9;
  border-radius: 8px;
}

.template-icon {
  width: 33px;
  height: 33px;
  border-radius: 50%;
  background-color: #f2f3f5;
}

:deep(.arco-modal-header) {
  border-bottom: none;
  padding: 16px 20px;
}

:deep(.arco-modal-body) {
  padding: 0 20px 20px;
}

:deep(.arco-modal-footer) {
  border-top: none;
  padding: 0 20px 20px;
}

:deep(.arco-form-item-label-col) {
  font-weight: normal;
}
</style>
