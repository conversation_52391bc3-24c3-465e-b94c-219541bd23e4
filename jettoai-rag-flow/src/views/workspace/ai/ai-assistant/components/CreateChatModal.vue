<template>
  <a-modal
    :visible="modelValue"
    :title="title"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 600 ? 600 : '100%'"
    draggable
    @update:visible="$emit('update:modelValue', $event)"
    @close="handleCancel"
  >
    <AiForm ref="formRef" v-model="form" :columns="columns">
      <template #file>
        <div class="w-full flex flex-col">
          <div>
            <AiFileUpload
              v-model="form.file"
              inputsKey="file"
              :tips="'请上传.docx 等格式文档'"
              :accept="'.docx'"
              @uploadFile="onUploadFile"
            />
          </div>
        </div>
      </template>
      <template #prompt>
        <div class="w-full flex flex-col">
          <md-editor v-model="form.prompt" style="height: 240px" :preview="false" :toolbars="toolbars" />
        </div>
      </template>
    </AiForm>
    <template #footer>
      <div class="flex justify-end">
        <a-button class="mr-2" @click="handleCancel">取消</a-button>
        <a-button type="primary" :loading="submitting" @click="handleSubmit">创建</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, h } from 'vue'
import { Message } from '@arco-design/web-vue'
import { useWindowSize } from '@vueuse/core'
import { createAssistant } from '@/apis/ai-assistant'
import { type ColumnItem } from '@/components/AiForm'
import { useResetReactive } from '@/hooks'
import { MdEditor } from 'md-editor-v3'
import 'md-editor-v3/lib/style.css'
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '新建会话'
  }
})
const toolbars = [
  'bold',
  'italic',
  'strikethrough',
  'heading',
  'quote',
  'list',
  'ordered-list',
  'unordered-list',
  'code',
  'code-block',
  'link',
  'image',
  'table',
  'preview',
  'fullscreen'
]
const emit = defineEmits(['update:modelValue', 'cancel', 'submit'])

// 窗口大小
const { width } = useWindowSize()

// 表单引用
const formRef = ref()

// 提交状态
const submitting = ref(false)

// 类型选项
const typeOptions = [
  { label: '测试用例生成助手', value: 'test-case' },
  { label: '智能问答助手', value: 'qa' },
  { label: '代码审查助手', value: 'code-review' },
  { label: '文章写作助手', value: 'article' }
]
const availableSystems = ref(['系统1', '系统2', '系统3'])

// 表单数据
const [form, resetForm] = useResetReactive({
  file: File,
  sys_names: [],
  split_level: 0,
  prompt: ''
})

// 版本选项
const versionOptions = [
  { label: 'V1.2', value: 'V1.2' },
  { label: 'V1.8', value: 'V1.8' },
  { label: 'V2.4', value: 'V2.4' }
]

// 表单列定义
const columns = reactive<ColumnItem[]>([
  {
    label: '上传文档',
    field: 'file',
    span: 24,
    rules: [{ required: true, message: '请选上传文档' }]
  },
  {
    label: '分块级别',
    field: 'split_level',
    type: 'input-number',
    span: 24,
    required: true,
    props: {
      placeholder: '请输入分块级别'
    }
  },
  {
    label: '涉及系统',
    field: 'sys_names',
    type: 'select',
    span: 24,
    required: true,
    props: {
      placeholder: '请选择',
      options: availableSystems.value,
      multiple: true,
      allowCreate: true
    }
  },
  {
    label: '提示词',
    field: 'prompt',
    type: 'input',
    span: 24,
    required: true,
    props: {
      placeholder: '请输入提示词',
      options: versionOptions
    }
  }
])

// 取消处理
const handleCancel = () => {
  emit('update:modelValue', false)
  emit('cancel')
  resetForm()
}
// const onUploadFile = async (file: File, callback?: (res) => void) => {
//   console.log(file)
//   form.file = file

//   if (callback) {
//     callback(file)
//   }
// }
const onUploadFile = async (
  file: File,
  onProgress?: (percent: number) => void,
  key?: string,
  transfer_method?: string,
  callback?: (res) => void
) => {
  onProgress(100)
  form.file = file
  if (callback) {
    callback(file)
  }
}
// 提交处理
const handleSubmit = async () => {
  try {
    const isInvalid = await formRef.value?.formRef?.validate()
    if (isInvalid) return

    submitting.value = true
    await createAssistant(form)
    Message.success('创建成功')
    emit('update:modelValue', false)
    emit('submit', { ...form })
    resetForm()
  } catch (error) {
    console.error('创建助手失败', error)
    Message.error('创建助手失败，请稍后重试')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.analysis-container {
  border: 1px solid #f0f0f0;
}

.template-bg {
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.76), rgba(255, 255, 255, 0.8));
  border: 1px solid #d9d9d9;
  border-radius: 8px;
}

.template-icon {
  width: 33px;
  height: 33px;
  border-radius: 50%;
  background-color: #f2f3f5;
}

:deep(.arco-modal-header) {
  border-bottom: none;
  padding: 16px 20px;
}

:deep(.arco-modal-body) {
  padding: 0 20px 20px;
}

:deep(.arco-modal-footer) {
  border-top: none;
  padding: 0 20px 20px;
}

:deep(.arco-form-item-label-col) {
  font-weight: normal;
}
</style>
