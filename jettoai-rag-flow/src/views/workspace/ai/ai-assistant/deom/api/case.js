import request from '@/utils/request'

// 生成案例
export function generateCase(taskId, file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: `/case/generate/${taskId}`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 导出案例
export function exportCase(taskId) {
  return request({
    url: `/case/export/${taskId}`,
    method: 'get',
    responseType: 'blob'
  })
} 