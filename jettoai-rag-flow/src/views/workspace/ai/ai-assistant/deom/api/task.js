import axios from 'axios'

const api = axios.create({
  baseURL: '/api/kl'
})

export const taskApi = {
  // 获取任务列表
  getTasks(params) {
    return api.get('/tasks', { params })
  },

  // 获取单个任务详情
  getTask(taskId) {
    return api.get(`/tasks/${taskId}`)
  },

  // 获取默认提示词
  getDefaultPrompt(phase) {
    return api.get(`/default-prompt/${phase}`)
  },

  // 上传文档并创建任务
  createTask(formData) {
    return api.post('/elements/extract', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 重新生成要素
  regenerateElements(taskId, formData) {
    return api.post(`/elements/regenerate/${taskId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 导出要素抽取结果
  exportElements(taskId) {
    return api.get(`/elements/export/${taskId}`, {
      responseType: 'blob'
    })
  },

  // 上传正交组合 Excel
  uploadOrthogonal(taskId, formData) {
    return api.post('/orthogonal/generate', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 导出正交组合结果
  exportOrthogonal(taskId) {
    return api.get(`/orthogonal/export/${taskId}`, {
      responseType: 'blob'
    })
  },

  // 重新生成正交组合
  regenerateOrthogonal(taskId, data) {
    return api.post(`/orthogonal/regenerate/${taskId}`, data)
  },

  // 生成案例
  generateCase(taskId, file) {
    const formData = new FormData()
    formData.append('file', file)
    return api.post(`/case/generate/${taskId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 导出案例
  exportCase(taskId) {
    return api.get(`/case/export/${taskId}`, {
      responseType: 'blob'
    })
  }
} 