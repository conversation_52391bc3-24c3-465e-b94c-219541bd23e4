<template>
  <div class="case-generator">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>案例生成</span>
          <span class="status-tag" :class="statusClass">{{ statusText }}</span>
        </div>
      </template>
      
      <div class="upload-section">
        <el-upload
          v-if="canUpload"
          class="upload-demo"
          :action="null"
          :auto-upload="false"
          :on-change="handleFileChange"
          :show-file-list="true"
          :limit="1"
          accept=".xlsx,.xls"
        >
          <template #trigger>
            <el-button type="primary">选择文件</el-button>
          </template>
          <template #tip>
            <div class="el-upload__tip">
              请上传Excel文件 (.xlsx, .xls)
            </div>
          </template>
        </el-upload>

        <el-button 
          v-if="canUpload"
          type="success" 
          :disabled="!selectedFile || isGenerating"
          @click="handleGenerate"
          :loading="isGenerating"
        >
          {{ isGenerating ? '生成中...' : '生成案例' }}
        </el-button>

        <el-button 
          v-if="canExport"
          type="warning" 
          @click="handleExport"
        >
          导出案例
        </el-button>

        <el-button 
          v-if="taskStatus === 'FAILED'"
          type="danger"
          @click="handleRetry"
        >
          重新生成
        </el-button>
      </div>

      <div v-if="errorMessage" class="error-message">
        <el-alert
          :title="errorMessage"
          type="error"
          :closable="true"
          @close="errorMessage = ''"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { generateCase, exportCase } from '@/api/case'

export default {
  name: 'CaseGenerator',
  props: {
    taskId: {
      type: String,
      required: true
    },
    taskStatus: {
      type: String,
      required: true
    }
  },
  setup(props) {
    const selectedFile = ref(null)
    const isGenerating = ref(false)
    const errorMessage = ref('')

    const statusText = computed(() => {
      switch (props.taskStatus) {
        case 'NOT_STARTED':
          return '未开始'
        case 'PROCESSING':
          return '处理中'
        case 'SUCCEEDED':
          return '已完成'
        case 'FAILED':
          return '失败'
        default:
          return '未知状态'
      }
    })

    const statusClass = computed(() => {
      switch (props.taskStatus) {
        case 'NOT_STARTED':
          return 'status-not-started'
        case 'PROCESSING':
          return 'status-processing'
        case 'SUCCEEDED':
          return 'status-succeeded'
        case 'FAILED':
          return 'status-failed'
        default:
          return ''
      }
    })

    const canUpload = computed(() => {
      return !props.taskStatus || props.taskStatus === 'NOT_STARTED' || props.taskStatus === 'FAILED'
    })

    const canExport = computed(() => {
      return props.taskStatus === 'SUCCEEDED'
    })

    const handleFileChange = (file) => {
      selectedFile.value = file.raw
      errorMessage.value = ''
    }

    const handleGenerate = async () => {
      if (!selectedFile.value) {
        ElMessage.warning('请先选择文件')
        return
      }

      isGenerating.value = true
      errorMessage.value = ''

      try {
        await generateCase(props.taskId, selectedFile.value)
        ElMessage.success('案例生成任务已创建')
      } catch (error) {
        errorMessage.value = error.response?.data?.detail || '生成案例失败'
        ElMessage.error(errorMessage.value)
      } finally {
        isGenerating.value = false
      }
    }

    const handleExport = async () => {
      try {
        const response = await exportCase(props.taskId)
        const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `case_result_${props.taskId}.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
        ElMessage.success('导出成功')
      } catch (error) {
        ElMessage.error('导出失败：' + (error.response?.data?.detail || error.message))
      }
    }

    const handleRetry = () => {
      selectedFile.value = null
      errorMessage.value = ''
    }

    return {
      selectedFile,
      isGenerating,
      errorMessage,
      statusText,
      statusClass,
      canUpload,
      canExport,
      handleFileChange,
      handleGenerate,
      handleExport,
      handleRetry
    }
  }
}
</script>

<style scoped>
.case-generator {
  margin: 20px;
}

.upload-section {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.error-message {
  margin-top: 20px;
}

.el-upload__tip {
  margin-top: 8px;
  color: #666;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-tag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
}

.status-not-started {
  background-color: #909399;
  color: white;
}

.status-processing {
  background-color: #409EFF;
  color: white;
}

.status-succeeded {
  background-color: #67C23A;
  color: white;
}

.status-failed {
  background-color: #F56C6C;
  color: white;
}
</style> 