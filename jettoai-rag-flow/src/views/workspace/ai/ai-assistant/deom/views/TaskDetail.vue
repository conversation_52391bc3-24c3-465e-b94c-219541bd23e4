<template>
  <div class="task-detail">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>任务详情</span>
        </div>
      </template>
      
      <div class="task-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务ID">{{ task.id }}</el-descriptions-item>
          <el-descriptions-item label="任务名称">{{ task.name }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ task.create_time }}</el-descriptions-item>
          <el-descriptions-item label="状态">{{ task.status }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 案例生成组件 -->
      <case-generator
        :task-id="task.id"
        :task-status="task.phase3_status"
      />
    </el-card>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import CaseGenerator from '@/components/CaseGenerator.vue'
import { getTaskDetail } from '@/api/task'

export default {
  name: 'TaskDetail',
  components: {
    CaseGenerator
  },
  setup() {
    const route = useRoute()
    const task = ref({})

    const fetchTaskDetail = async () => {
      try {
        const response = await getTaskDetail(route.params.id)
        task.value = response.data
      } catch (error) {
        ElMessage.error('获取任务详情失败：' + error.message)
      }
    }

    onMounted(() => {
      fetchTaskDetail()
    })

    return {
      task
    }
  }
}
</script>

<style scoped>
.task-detail {
  margin: 20px;
}

.task-info {
  margin-bottom: 20px;
}
</style> 