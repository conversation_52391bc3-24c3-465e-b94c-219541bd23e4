<template>
  <div class="task-list">
    <div class="header">
      <h2>任务列表</h2>
      <el-upload
        class="upload-btn"
        :action="null"
        :auto-upload="false"
        :show-file-list="false"
        accept=".docx"
        @change="handleFileSelect"
      >
        <el-button type="primary" :icon="Upload">上传文档</el-button>
      </el-upload>
    </div>

    <el-form :inline="true" :model="searchForm" class="search-form">
      <el-form-item label="任务名称">
        <el-input v-model="searchForm.name" placeholder="请输入任务名称" clearable />
      </el-form-item>
      <el-form-item label="时间范围">
        <el-date-picker
          v-model="searchForm.timeRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="tasks" v-loading="loading" border>
      <el-table-column prop="id" label="任务ID" width="180" />
      <el-table-column prop="name" label="任务名称" min-width="200" />
      <el-table-column label="要素抽取" min-width="200">
        <template #default="{ row }">
          <div class="status-cell">
            <el-tag :type="getStatusType(row.phases.phase1.status)">
              {{ getStatusText(row.phases.phase1.status) }}
            </el-tag>
            <el-button
              v-if="row.phases.phase1.status === 'succeeded'"
              type="primary"
              link
              @click="handleExportElements(row.id)"
            >
              导出
            </el-button>
            <el-button
              v-if="['succeeded', 'failed', 'partially_succeeded'].includes(row.phases.phase1.status)"
              type="primary"
              link
              @click="handleRegenerateElements(row)"
            >
              重新生成
            </el-button>
            <el-tooltip
              v-if="row.phases.phase1.error"
              :content="row.phases.phase1.error"
              placement="top"
            >
              <el-icon><Warning /></el-icon>
            </el-tooltip>
            <div class="time-info" v-if="row.phases.phase1.start_time || row.phases.phase1.end_time">
              <div v-if="row.phases.phase1.start_time">开始：{{ formatTime(row.phases.phase1.start_time) }}</div>
              <div v-if="row.phases.phase1.end_time">结束：{{ formatTime(row.phases.phase1.end_time) }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="正交组合" min-width="200">
        <template #default="{ row }">
          <div class="status-cell">
            <el-tag :type="getStatusType(row.phases.phase2.status)">
              {{ getStatusText(row.phases.phase2.status) }}
            </el-tag>
            <template v-if="row.phases.phase2.status === 'not_started'">
              <el-upload
                :action="null"
                :auto-upload="false"
                :show-file-list="false"
                accept=".xlsx,.xls"
                @change="(file) => handleOrthogonalUpload(row.id, file)"
              >
                <el-button type="primary" link>导入</el-button>
              </el-upload>
            </template>
            <template v-else-if="['succeeded', 'partially_succeeded', 'failed'].includes(row.phases.phase2.status)">
              <el-upload
                :action="null"
                :auto-upload="false"
                :show-file-list="false"
                accept=".xlsx,.xls"
                @change="(file) => handleOrthogonalUpload(row.id, file)"
              >
                <el-button type="primary" link>重新导入生成</el-button>
              </el-upload>
              <el-button v-if="['succeeded', 'partially_succeeded'].includes(row.phases.phase2.status)" type="primary" link @click="handleExportOrthogonal(row.id)">
                导出
              </el-button>
            </template>
            <template v-if="!['not_started', 'processing'].includes(row.phases.phase2.status)">
              <el-button type="primary" link @click="handleRegenerate(row)">
                重新生成
              </el-button>
            </template>
            <el-tooltip
              v-if="row.phases.phase2.error"
              :content="row.phases.phase2.error"
              placement="top"
            >
              <el-icon><Warning /></el-icon>
            </el-tooltip>
            <div class="time-info" v-if="row.phases.phase2.start_time || row.phases.phase2.end_time">
              <div v-if="row.phases.phase2.start_time">开始：{{ formatTime(row.phases.phase2.start_time) }}</div>
              <div v-if="row.phases.phase2.end_time">结束：{{ formatTime(row.phases.phase2.end_time) }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="案例生成" min-width="200">
        <template #default="{ row }">
          <div class="status-cell">
            <el-tag :type="getStatusType(row.phases.phase3.status)">
              {{ getStatusText(row.phases.phase3.status) }}
            </el-tag>
            <template v-if="!row.phases.phase3.status || row.phases.phase3.status === 'not_started' || row.phases.phase3.status === 'failed' || row.phases.phase3.status === 'succeeded' || row.phases.phase3.status === 'partially_succeeded'">
              <el-upload
                :action="null"
                :auto-upload="false"
                :show-file-list="false"
                accept=".xlsx,.xls"
                @change="(file) => handleFileChange(file, row)"
              >
                <el-button type="primary" link>{{ ['succeeded', 'partially_succeeded'].includes(row.phases.phase3.status) ? '重新导入生成' : '导入' }}</el-button>
              </el-upload>
            </template>
            <el-button
              v-if="row.phases.phase3.status === 'succeeded' || row.phases.phase3.status === 'partially_succeeded'"
              type="primary"
              link
              @click="handleExport(row)"
            >
              导出
            </el-button>
            <el-tooltip
              v-if="row.phases.phase3.error"
              :content="row.phases.phase3.error"
              placement="top"
            >
              <el-icon><Warning /></el-icon>
            </el-tooltip>
            <div class="time-info" v-if="row.phases.phase3.start_time || row.phases.phase3.end_time">
              <div v-if="row.phases.phase3.start_time">开始：{{ formatTime(row.phases.phase3.start_time) }}</div>
              <div v-if="row.phases.phase3.end_time">结束：{{ formatTime(row.phases.phase3.end_time) }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 重新生成弹窗 -->
    <el-dialog
      v-model="regenerateDialogVisible"
      title="重新生成正交组合"
      width="500px"
    >
      <el-form :model="regenerateForm" label-width="100px">
        <el-form-item label="类别">
          <el-select
            v-model="regenerateForm.categories"
            multiple
            placeholder="请选择类别"
            style="width: 100%"
          >
            <el-option
              v-for="category in availableCategories"
              :key="category"
              :label="category"
              :value="category"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="重新生成全部">
          <el-switch v-model="regenerateForm.regenerateAll" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="regenerateDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmRegenerate">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 上传文件对话框 -->
    <el-dialog
      v-model="uploadDialogVisible"
      :title="isRegenerating ? '重新生成要素' : '上传文档'"
      width="800px"
    >
      <el-form :model="uploadForm" label-width="100px">
        <el-form-item label="分块级别">
          <el-input-number 
            v-model="uploadForm.splitLevel" 
            :min="0"
            :precision="0"
            placeholder="请输入分块级别"
          />
        </el-form-item>
        <el-form-item label="涉及系统">
          <div class="system-select">
            <el-select
              v-model="uploadForm.sysNames"
              multiple
              filterable
              allow-create
              default-first-option
              placeholder="请选择或输入系统名称"
              style="width: 100%"
            >
              <el-option
                v-for="sys in availableSystems"
                :key="sys"
                :label="sys"
                :value="sys"
              />
            </el-select>
          </div>
        </el-form-item>
        <el-form-item label="提示词">
          <div class="prompt-editor">
            <md-editor
              v-model="uploadForm.prompt"
              :preview="false"
              :toolbars="toolbars"
              @onChange="handlePromptChange"
            />
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="handleDialogConfirm">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 重新生成要素弹窗 -->
    <el-dialog
      v-model="regenerateElementsDialogVisible"
      title="重新生成要素"
      width="800px"
    >
      <el-form :model="regenerateElementsForm" label-width="100px">
        <el-form-item label="分块级别">
          <el-input-number 
            v-model="regenerateElementsForm.splitLevel" 
            :min="0"
            :precision="0"
            placeholder="请输入分块级别"
          />
        </el-form-item>
        <el-form-item label="涉及系统">
          <div class="system-select">
            <el-select
              v-model="regenerateElementsForm.sysNames"
              multiple
              filterable
              allow-create
              default-first-option
              placeholder="请选择或输入系统名称"
              style="width: 100%"
            >
              <el-option
                v-for="sys in availableSystems"
                :key="sys"
                :label="sys"
                :value="sys"
              />
            </el-select>
          </div>
        </el-form-item>
        <el-form-item label="提示词">
          <div class="prompt-editor">
            <md-editor
              v-model="regenerateElementsForm.prompt"
              :preview="false"
              :toolbars="toolbars"
              @onChange="handleRegeneratePromptChange"
            />
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="regenerateElementsDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmRegenerateElements">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload, Warning } from '@element-plus/icons-vue'
import { taskApi } from '@/api/task'
import { MdEditor } from 'md-editor-v3'
import 'md-editor-v3/lib/style.css'
import { useRouter } from 'vue-router'

// 状态和数据
const loading = ref(false)
const tasks = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const regenerateDialogVisible = ref(false)
const currentTask = ref(null)
const availableCategories = ref([])
const uploadDialogVisible = ref(false)
const selectedFile = ref(null)
const availableSystems = ref(['系统1', '系统2', '系统3']) // 预设的系统选项
const isRegenerating = ref(false) // 是否是重新生成状态

// Markdown编辑器工具栏配置
const toolbars = [
  'bold',
  'italic',
  'strikethrough',
  'heading',
  'quote',
  'list',
  'ordered-list',
  'unordered-list',
  'code',
  'code-block',
  'link',
  'image',
  'table',
  'preview',
  'fullscreen'
]

const searchForm = reactive({
  name: '',
  timeRange: []
})

const regenerateForm = reactive({
  categories: [],
  regenerateAll: false
})

const uploadForm = reactive({
  splitLevel: 0,
  sysNames: [],
  prompt: ''  // 初始化为空字符串
})

// 重新生成要素相关
const regenerateElementsDialogVisible = ref(false)
const currentRegenerateTask = ref(null)
const regenerateElementsForm = reactive({
  splitLevel: 0,
  sysNames: [],
  prompt: ''
})

const router = useRouter()

// 获取默认提示词
const loadDefaultPrompt = async () => {
  try {
    const response = await taskApi.getDefaultPrompt('phase1')
    if (response.data.code === 200) {
      uploadForm.prompt = response.data.data.prompt
    }
  } catch (error) {
    ElMessage.error('加载默认提示词失败')
  }
}

// 方法
const getStatusType = (status) => {
  const statusMap = {
    'not_started': 'info',
    'processing': 'warning',
    'succeeded': 'success',
    'failed': 'danger',
    'partially_succeeded': 'warning'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'not_started': '未开始',
    'processing': '处理中',
    'succeeded': '成功',
    'failed': '失败',
    'partially_succeeded': '部分成功'
  }
  return statusMap[status] || status
}

const loadTasks = async () => {
  loading.value = true
  try {
    const params = {
      skip: (currentPage.value - 1) * pageSize.value,
      limit: pageSize.value,
      name: searchForm.name,
      start_time: searchForm.timeRange[0],
      end_time: searchForm.timeRange[1]
    }
    const response = await taskApi.getTasks(params)
    tasks.value = response.data.data.items
    total.value = response.data.data.total
  } catch (error) {
    ElMessage.error('加载任务列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadTasks()
}

const resetSearch = () => {
  searchForm.name = ''
  searchForm.timeRange = []
  handleSearch()
}

const handleSizeChange = (val) => {
  pageSize.value = val
  loadTasks()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadTasks()
}

const handleFileSelect = async (file) => {
  selectedFile.value = file.raw
  isRegenerating.value = false
  uploadDialogVisible.value = true
  // 在打开对话框时加载默认提示词
  await loadDefaultPrompt()
}

const handleRegenerateElements = async (task) => {
  currentTask.value = task
  isRegenerating.value = true
  uploadForm.splitLevel = task.split_level
  try {
    // 尝试解析系统名称，如果失败则尝试按逗号分割
    try {
      uploadForm.sysNames = JSON.parse(task.sys_names)
    } catch (error) {
      // 如果JSON解析失败，尝试按逗号分割
      if (typeof task.sys_names === 'string') {
        uploadForm.sysNames = task.sys_names.split(',').map(name => name.trim())
      } else if (Array.isArray(task.sys_names)) {
        uploadForm.sysNames = task.sys_names
      } else {
        console.warn('系统名称格式不正确:', task.sys_names)
        uploadForm.sysNames = []
      }
    }
  } catch (error) {
    console.error('解析系统名称失败:', error)
    uploadForm.sysNames = []
  }
  uploadForm.prompt = task.phases.phase1.prompt
  uploadDialogVisible.value = true
}

const handleDialogClose = () => {
  uploadDialogVisible.value = false
  selectedFile.value = null
  uploadForm.sysNames = []
  uploadForm.splitLevel = 0
  uploadForm.prompt = ''
  isRegenerating.value = false
  currentTask.value = null
}

const handleDialogConfirm = async () => {
  if (isRegenerating.value) {
    // 重新生成要素
    if (!currentTask.value) return
    
    const formData = new FormData()
    if (uploadForm.splitLevel !== currentTask.value.split_level) {
      formData.append('split_level', uploadForm.splitLevel.toString())
    }
    if (JSON.stringify(uploadForm.sysNames) !== JSON.stringify(currentTask.value.sys_names)) {
      formData.append('sys_names', JSON.stringify(uploadForm.sysNames))
    }
    if (uploadForm.prompt !== currentTask.value.phases.phase1.prompt) {
      formData.append('prompt', uploadForm.prompt)
    }

    try {
      await taskApi.regenerateElements(currentTask.value.id, formData)
      ElMessage.success('重新生成任务已创建')
      handleDialogClose()
      loadTasks()
    } catch (error) {
      ElMessage.error('重新生成任务创建失败')
    }
  } else {
    // 创建新任务
    if (!selectedFile.value) return
    const formData = new FormData()
    formData.append('file', selectedFile.value)
    formData.append('sys_names', JSON.stringify(uploadForm.sysNames))
    formData.append('split_level', uploadForm.splitLevel.toString())
    formData.append('prompt', uploadForm.prompt)

    try {
      await taskApi.createTask(formData)
      ElMessage.success('任务创建成功')
      handleDialogClose()
      loadTasks()
    } catch (error) {
      ElMessage.error('任务创建失败')
    }
  }
}

const handlePromptChange = (value) => {
  uploadForm.prompt = value
}

const handleExportElements = async (taskId) => {
  try {
    const response = await taskApi.exportElements(taskId)
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `elements_${taskId}.xlsx`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const handleOrthogonalUpload = async (taskId, file) => {
  if (!file) return
  const formData = new FormData()
  formData.append('file', file.raw)
  formData.append('task_id', taskId)

  try {
    await taskApi.uploadOrthogonal(taskId, formData)
    ElMessage.success('正交组合文件上传成功')
    loadTasks()
  } catch (error) {
    ElMessage.error('正交组合文件上传失败：' + (error.response?.data?.detail || error.message))
  }
}

const handleExportOrthogonal = async (taskId) => {
  try {
    const response = await taskApi.exportOrthogonal(taskId)
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `orthogonal_${taskId}.xlsx`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const handleRegenerate = (task) => {
  currentTask.value = task
  availableCategories.value = task.categories || []
  regenerateForm.categories = task.phases.phase2.error_categories || []
  regenerateForm.regenerateAll = false
  regenerateDialogVisible.value = true
}

const confirmRegenerate = async () => {
  if (!currentTask.value) return
  try {
    await taskApi.regenerateOrthogonal(currentTask.value.id, regenerateForm)
    ElMessage.success('重新生成任务已创建')
    regenerateDialogVisible.value = false
    loadTasks()
  } catch (error) {
    ElMessage.error('重新生成任务创建失败')
  }
}

const handleRegeneratePromptChange = (value) => {
  regenerateElementsForm.prompt = value
}

const confirmRegenerateElements = async () => {
  if (!currentRegenerateTask.value) return
  
  const formData = new FormData()
  if (regenerateElementsForm.splitLevel !== currentRegenerateTask.value.split_level) {
    formData.append('split_level', regenerateElementsForm.splitLevel.toString())
  }
  if (JSON.stringify(regenerateElementsForm.sysNames) !== JSON.stringify(currentRegenerateTask.value.sys_names)) {
    formData.append('sys_names', JSON.stringify(regenerateElementsForm.sysNames))
  }
  if (regenerateElementsForm.prompt !== currentRegenerateTask.value.phases.phase1.prompt) {
    formData.append('prompt', regenerateElementsForm.prompt)
  }

  try {
    await taskApi.regenerateElements(currentRegenerateTask.value.id, formData)
    ElMessage.success('重新生成任务已创建')
    regenerateElementsDialogVisible.value = false
    loadTasks()
  } catch (error) {
    ElMessage.error('重新生成任务创建失败')
  }
}

const handleView = (row) => {
  router.push(`/task/${row.id}`)
}

const handleFileChange = async (file, row) => {
  if (!file || !row) return
  
  try {
    await taskApi.generateCase(row.id, file.raw)
    ElMessage.success('案例生成任务已创建')
    loadTasks()
  } catch (error) {
    ElMessage.error('生成案例失败：' + (error.response?.data?.detail || error.message))
  }
}

const handleExport = async (row) => {
  try {
    const response = await taskApi.exportCase(row.id)
    const blob = new Blob([response.data], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${row.name.split('.')[0]}_case_result.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败：' + (error.response?.data?.detail || error.message))
  }
}

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  // 添加8小时以转换为北京时间
  date.setHours(date.getHours() + 8)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    timeZone: 'Asia/Shanghai'
  })
}

// 生命周期钩子
onMounted(() => {
  loadTasks()
})
</script>

<style scoped>
.task-list {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 20px;
}

.status-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.upload-btn {
  display: inline-block;
}

.system-select {
  width: 100%;
}

.prompt-editor {
  width: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.time-info {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style> 