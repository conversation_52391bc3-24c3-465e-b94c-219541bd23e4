<template>
  <AiPageLayout :padding="false" :margin="false">
    <!-- 应用助手部分 -->
    <div class="section-container mb-6">
      <AssistantCarousel :assistant-list="assistantList" @create="handleCreate" />
    </div>

    <!-- 会话记录标题 -->
    <div class="section-title mb-4">
      <h2 class="text-xl font-normal">会话记录</h2>
    </div>

    <!-- 任务执行记录部分 -->
    <div class="section-container" style="overflow: hidden">
      <!-- 表格区域 -->
      <AiTable
        row-key="id"
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :scroll="{ x: '100%', y: '100%' }"
        @change="handleTableChange"
        @refresh="fetchData"
      >
        <!-- 自定义工具栏 -->
        <template #toolbar-left>
          <div class="flex items-center">
            <div class="mr-6 flex items-center">
              <span class="mr-2">会话名称：</span>
              <a-input v-model="queryParams.name" placeholder="请输入" style="width: 220px" />
            </div>
            <div class="mr-6 flex items-center">
              <span class="mr-2">智能体版本：</span>
              <a-select v-model="queryParams.version" placeholder="请选择" style="width: 220px">
                <a-option v-for="version in versionOptions" :key="version.value" :value="version.value">
                  {{ version.label }}
                </a-option>
              </a-select>
            </div>
            <a-button type="primary" class="mr-2" @click="handleSearch">查 询</a-button>
            <a-button @click="resetSearch">重 置</a-button>
          </div>
        </template>
        <template #toolbar-right>
          <a-button type="primary" @click="handleReimport()">
            <!-- <template #icon><icon-plus /></template> -->
            <template #default>新建会话</template>
          </a-button>
        </template>
        <!-- 序号列 -->
        <template #index="{ rowIndex }">
          {{ formatIndex(rowIndex) }}
        </template>

        <!-- 会话名称列 -->
        <template #name="{ record }">
          <div class="flex items-center">
            <span>{{ record.name || record.taskName || record.assistantName }}</span>
          </div>
        </template>

        <!-- 智能体版本列 -->
        <template #version="{ record }">
          <span>{{ record.version || 'V1.8' }}</span>
        </template>

        <!-- 要素提取列 -->
        <template #phase1="{ record }">
          <div class="flex items-center">
            <!-- <div class="w-[30px] h-[30px] mr-4">
              <a-progress
                :stroke-width="3"
                :stroke-color="getProgressColor(record.phases.phase1.status)"
                size="small"
                type="circle"
                :percent="record.phases.phase1.status === 'succeeded' ? 1 : Math.random() * 0.8 + 0.1"
              >
                <template #text>
                  <span class="inline-block w-6">{{ record.progress }}</span>
                </template>
              </a-progress>
            </div> -->
            <a-space class="text-primary cursor-pointer">
              <a-tooltip :content="record.phases.phase1.error" placement="top">
                <icon-info-circle v-if="record.phases.phase1.error" style="color: #fa5f4b" />
              </a-tooltip>

              <a-tag v-if="record.phases.phase1.status === 'not_started'" class="text-gray-500">未开始</a-tag>
              <span v-if="record.phases.phase1.status === 'succeeded'" class="mr-2" @click="handleExport(record)">
                导出
              </span>
              <a-trigger position="top" auto-fit-position :unmount-on-close="false">
                <span
                  v-if="['succeeded', 'failed', 'partially_succeeded'].includes(record.phases.phase1.status)"
                  class="mr-2"
                  @click="handleRegenerate(record)"
                >
                  重新生成
                </span>
                <template #content>
                  <div class="demo-basic">
                    <h4 class="font-bold text-[14px]">要素提取</h4>
                    <div class="flex justify-between text-[12px] mt-4 mb-4">
                      <span>开始时间：</span>
                      <span>{{ record.phases.phase1.start_time }}</span>
                    </div>
                    <div class="flex justify-between text-[12px]">
                      <span>结束时间：</span>
                      <span class="clolor-gray">{{ record.phases.phase1.end_time }}</span>
                    </div>
                  </div>
                </template>
              </a-trigger>
            </a-space>
          </div>
        </template>

        <!-- 正交结合列 -->
        <template #phase2="{ record }">
          <div class="flex items-center">
            <!-- <div v-if="record.phases.phase2.status !== 'not_started'" class="w-[30px] h-[30px] mr-4">
              <a-progress
                :stroke-width="3"
                :stroke-color="getProgressColor(record.phases.phase1.status)"
                size="small"
                type="circle"
                :percent="record.phases.phase1.status === 'succeeded' ? 1 : Math.random() * 0.8 + 0.1"
              >
                <template #text>
                  <span class="inline-block w-6">
                    {{ record.phases.phase1.status === 'succeeded' ? 1 : Math.floor(Math.random() * 9) + 1 }}
                  </span>
                </template>
              </a-progress>
            </div> -->
            <a-space class="text-primary cursor-pointer">
              <a-tooltip :content="record.phases.phase2.error" placement="top">
                <icon-info-circle v-if="record.phases.phase2.error" style="color: #fa5f4b" />
              </a-tooltip>
              <a-tag v-if="record.phases.phase2.status === 'not_started'" class="text-gray-500">未开始</a-tag>
              <a-upload
                v-if="record.phases.phase2.status === 'not_started'"
                :action="null"
                :auto-upload="false"
                :show-file-list="false"
                accept=".xlsx,.xls"
                @change="(file) => handleOrthogonalUpload(record.id, file)"
              >
                <template #upload-button>
                  <span>导入</span>
                </template>
              </a-upload>

              <span v-if="record.phases.phase2.status === 'succeeded'" class="mr-2" @click="handleExport(record)">
                导出
              </span>
              <a-trigger position="top" auto-fit-position :unmount-on-close="false">
                <span
                  v-if="['succeeded', 'failed', 'partially_succeeded'].includes(record.phases.phase2.status)"
                  class="mr-2"
                  @click="handleRegenerate(record)"
                >
                  重新生成
                </span>
                <template #content>
                  <div class="demo-basic">
                    <h4 class="font-bold text-[14px]">要素提取</h4>
                    <div class="flex justify-between text-[12px] mt-4 mb-4">
                      <span>开始时间：</span>
                      <span>{{ record.phases.phase2.start_time }}</span>
                    </div>
                    <div class="flex justify-between text-[12px]">
                      <span>结束时间：</span>
                      <span class="clolor-gray">{{ record.phases.phase2.end_time }}</span>
                    </div>
                  </div>
                </template>
              </a-trigger>
            </a-space>
          </div>
        </template>

        <!-- 案例生成列 -->
        <template #phase3="{ record }">
          <div class="flex items-center">
            <!-- <div v-if="record.phases.phase3.status !== 'not_started'" class="w-[30px] h-[30px] mr-4">
              <a-progress
                :stroke-width="3"
                :stroke-color="getProgressColor(record.phases.phase2.status)"
                size="small"
                type="circle"
                :percent="record.phases.phase3.status === 'succeeded' ? 1 : Math.random() * 0.8 + 0.1"
              >
                <template #text>
                  <span class="inline-block w-6">
                    {{ record.phases.phase3.status === 'succeeded' ? 1 : Math.floor(Math.random() * 9) + 1 }}
                  </span>
                </template>
              </a-progress>
            </div> -->
            <a-space class="text-primary cursor-pointer">
              <a-tooltip :content="record.phases.phase3.error" placement="top">
                <icon-info-circle v-if="record.phases.phase3.error" style="color: #fa5f4b" />
              </a-tooltip>

              <a-tag v-if="record.phases.phase3.status === 'not_started'" class="text-gray-500">未开始</a-tag>
              <span v-if="record.phases.phase3.status === 'succeeded'" class="mr-2" @click="handleExport(record)">
                导出
              </span>
              <a-trigger position="top" auto-fit-position :unmount-on-close="false">
                <span
                  v-if="['succeeded', 'failed', 'partially_succeeded'].includes(record.phases.phase3.status)"
                  class="mr-2"
                  @click="handleRegenerate(record)"
                >
                  重新生成
                </span>
                <template #content>
                  <div class="demo-basic">
                    <h4 class="font-bold text-[14px]">案例生成</h4>
                    <div class="flex justify-between text-[12px] mt-4 mb-4">
                      <span>开始时间：</span>
                      <span>{{ record.phases.phase3.start_time }}</span>
                    </div>
                    <div class="flex justify-between text-[12px]">
                      <span>结束时间：</span>
                      <span class="clolor-gray">{{ record.phases.phase3.end_time }}</span>
                    </div>
                  </div>
                </template>
              </a-trigger>
            </a-space>
          </div>
        </template>

        <!-- 会话创建时间列 -->
        <template #createTime="{ record }">
          <span>{{ record.createTime || record.executeTime }}</span>
        </template>

        <!-- 评测列 -->
        <template #assessment="{ record }">
          <div
            v-if="record.assessment || (record.evaluation && record.evaluation !== '未评价')"
            class="flex items-center justify-center"
          >
            <div class="assessment-circle">
              <span>{{ getAssessmentValue(record) }}</span>
            </div>
          </div>
          <div v-else class="flex items-center justify-center">
            <a-tag class="status-tag">未开始</a-tag>
          </div>
        </template>

        <!-- 操作列 -->
        <template #operations="{ record }">
          <div class="text-primary cursor-pointer">
            <span class="mr-2" @click="handleDetail(record)">详情</span>
            <span class="mr-2" @click="handleTerminate(record)">终止</span>
            <span @click="handleDelete(record)">删除</span>
          </div>
        </template>
      </AiTable>
    </div>

    <!-- 创建助手模态窗口 -->
    <CreateAssistantModal v-model:visible="createModalVisible" @submit="handleCreateSuccess" />
    <!-- 导入窗口 -->
    <CreateChatModal v-model:visible="createChatModalVisible" @submit="handleCreateSuccess" />
  </AiPageLayout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import AssistantCarousel from './components/AssistantCarousel.vue'
import CreateAssistantModal from './components/CreateAssistantModal.vue'
import CreateChatModal from './components/CreateChatModal.vue'
import { getAssistantList, getTaskList } from '@/apis/ai-assistant'
import { useTable } from '@/hooks'

// 类型定义
interface Assistant {
  id: string
  name: string
  description: string
  icon: string
  updateTime: string
  count: string
  version: string
  type: string
}

interface TaskItem {
  id: string
  name?: string
  assistantId?: string
  assistantName?: string
  taskName?: string
  icon?: string
  version?: string
  extractProgress?: number
  combineProgress?: number
  generateProgress?: number
  progress?: number
  status: string
  createTime?: string
  executeTime?: string
  completeTime?: string
  evaluation?: string
  assessment?: string
}

// 查询参数
const queryParams = reactive({
  name: '',
  version: ''
})

// 助手列表数据
const assistantList = ref<Assistant[]>([])

// 版本选项
const versionOptions = [
  { label: 'V1.2', value: 'V1.2' },
  { label: 'V1.8', value: 'V1.8' },
  { label: 'V2.4', value: 'V2.4' }
]

// 表格列定义
const columns = [
  { title: '序号', slotName: 'index', width: 60 },
  { title: '会话名称', slotName: 'name', width: 180 },
  { title: '智能体版本', slotName: 'version', width: 110 },
  { title: '会话创建时间', slotName: 'created_at', width: 180 },
  { title: '要素提取', slotName: 'phase1', width: 300 },
  { title: '正交结合', slotName: 'phase2', width: 300 },
  { title: '案例生成', slotName: 'phase3', width: 300 },
  { title: '操作', slotName: 'operations', width: 160, fixed: 'right' }
]
const { tableData, loading, pagination, search, refresh } = useTable<TaskItem>(
  (page) => getTaskList({ ...queryParams, ...page }),
  { immediate: true, dataKey: 'items' }
)

// 格式化序号
const formatIndex = (index: number) => {
  return index < 9 ? `0${index + 1}` : `${index + 1}`
}
const handleOrthogonalUpload = async (taskId, file) => {
  console.log(taskId, file)
  if (!file) return
  // const formData = new FormData()
  // formData.append('file', file.raw)
  // formData.append('task_id', taskId)

  // try {
  //   await taskApi.uploadOrthogonal(taskId, formData)
  //   ElMessage.success('正交组合文件上传成功')
  //   loadTasks()
  // } catch (error) {
  //   ElMessage.error('正交组合文件上传失败：' + (error.response?.data?.detail || error.message))
  // }
}
// 获取进度条颜色
const getProgressColor = (progress: string) => {
  if (progress === 'succeeded') {
    return '#C1E89D'
  } else if (progress === 'failed') {
    return '#FEC5C1'
  } else if (progress === 'partially_succeeded') {
    return '#FEC5C1'
  }
}

// 获取评测值
const getAssessmentValue = (record: TaskItem) => {
  if (record.assessment) {
    return record.assessment
  }
  if (record.evaluation && record.evaluation !== '未评价') {
    return record.evaluation.replace(/[^0-9]/g, '') || '1'
  }
  return '1'
}

// 处理表格变化
const handleTableChange = (data: any) => {
  if (data.pagination) {
    pagination.current = data.pagination.current
    pagination.pageSize = data.pagination.pageSize
    refresh()
  }
}

// 处理搜索
const handleSearch = () => {
  search()
}

// 重置搜索
const resetSearch = () => {
  queryParams.name = ''
  queryParams.version = ''
  search()
}

// 创建助手模态窗口控制
const createModalVisible = ref(false)

// 处理创建任务
const handleCreate = () => {
  createModalVisible.value = true
}

// 处理创建助手成功
const handleCreateSuccess = () => {
  Message.success('创建助手成功')
  fetchData()
}

// 处理详情查看
const handleDetail = (record: TaskItem) => {
  Message.info(`查看详情: ${record.id}`)
}

// 处理终止任务
const handleTerminate = (record: TaskItem) => {
  Message.info(`终止任务: ${record.id}`)
}

// 处理继续任务
const handleContinue = (record: TaskItem) => {
  Message.info(`继续任务: ${record.id}`)
}

// 处理删除任务
const handleDelete = (record: TaskItem) => {
  Message.info(`删除任务: ${record.id}`)
}

// 处理导出
const handleExport = (record: TaskItem) => {
  Message.info(`导出: ${record.id}`)
}

// 处理重新生成
const handleRegenerate = (record: TaskItem) => {
  Message.info(`重新生成: ${record.id}`)
}

const createChatModalVisible = ref(false)
// 处理重新导入生成
const handleReimport = (record?: TaskItem) => {
  createChatModalVisible.value = true
}

// 获取助手列表数据
const fetchAssistantData = async () => {
  try {
    const res = await getAssistantList()
    if (res && res.data) {
      assistantList.value = res.data
    }
  } catch (error) {
    console.error('获取助手列表失败', error)
    Message.error('获取助手列表失败')
  }
}

// 获取所有数据
const fetchData = () => {
  fetchAssistantData()
  // refresh()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-primary {
  color: #5147ff;
  cursor: pointer;
}

.assessment-circle {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  /* background-color: #5147ff; */
  color: white;
  font-size: 12px;
}

/* 调整进度条组件的样式 */
:deep(.arco-progress-circle-wrapper) {
  width: 35px !important;
  height: 35px !important;
}
:deep(.arco-progress-circle-text) {
  font-size: 12px;
}

:deep(.arco-progress-circle) {
  transform: scale(0.9);
}

.status-tag {
  background-color: #f4f4f6;
  color: #999999;
  border: none;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

/* 覆盖表格样式 */
:deep(.arco-table-th) {
  background-color: #f5f7fa !important;
  font-weight: normal !important;
  color: rgba(0, 0, 0, 0.85) !important;
}

:deep(.arco-table-td) {
  border-bottom: 1px solid #e8e8e8 !important;
}
.demo-basic {
  padding: 20px;
  width: auto;
  background-color: var(--color-bg-popup);
  border-radius: 4px;
  box-shadow: 0 2px 8px #00000026;
}
</style>
