<template>
  <AiPageLayout :padding="false" :margin="false">
    <!-- 应用助手部分 -->
    <div class="section-container mb-6">
      <AssistantCarousel :assistant-list="assistantList" @create="handleCreate" />
    </div>

    <!-- 会话记录标题 -->
    <div class="section-title mb-4">
      <h2 class="text-xl font-normal">会话记录</h2>
    </div>

    <!-- 任务执行记录部分 -->
    <div class="section-container" style="overflow: hidden">
      <div v-if="tableData.length === 0" />
      <!-- 表格区域 -->
      <AiTable
        v-if="tableData.length > 0"
        row-key="id"
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :scroll="{ x: '100%', y: '100%' }"
        @change="handleTableChange"
        @refresh="fetchData"
      >
        <!-- 自定义工具栏 -->
        <template #toolbar-left>
          <div class="flex items-center">
            <div class="mr-6 flex items-center">
              <span class="mr-2">会话名称：</span>
              <a-input v-model="queryParams.name" placeholder="请输入" style="width: 220px" />
            </div>
            <div class="mr-6 flex items-center">
              <span class="mr-2">起始时间：</span>
              <a-range-picker v-model="timeRange" style="width: 250px" @change="handleTimeRangeChange" />
            </div>
            <a-button type="primary" class="mr-2" @click="handleSearch">查 询</a-button>
            <a-button @click="resetSearch">重 置</a-button>
          </div>
        </template>
        <template #toolbar-right>
          <a-button type="primary" @click="handleReimport()">
            <!-- <template #icon><icon-plus /></template> -->
            <template #default>新建会话</template>
          </a-button>
        </template>
        <!-- 自定义表头 -->
        <template #phase1Title>
          <div class="flex items-center">
            <div class="text-center w-[18px] h-[18px] rounded-[9px] text-[12px] bg-slate-300 mr-2">1</div>
            <span class="mr-2">要素提取</span>
            <a-trigger position="top" auto-fit-position :unmount-on-close="false">
              <icon-question-circle />
              <template #content>
                <div class="demo-basic">
                  <h4 class="font-bold text-[14px]">要素提取</h4>
                  <div class="text-[12px] mt-2">基于测试单元使用 AI对需求进行规则分析，并提取关键要素。</div>
                </div>
              </template>
            </a-trigger>
          </div>
        </template>

        <template #phase2Title>
          <div class="flex items-center">
            <div class="text-center w-[18px] h-[18px] rounded-[9px] text-[12px] bg-slate-300 mr-2">2</div>
            <span class="mr-2">正交结合</span>

            <a-trigger position="top" auto-fit-position :unmount-on-close="false">
              <icon-question-circle />
              <template #content>
                <div class="demo-basic">
                  <h4 class="font-bold text-[14px]">正交结合</h4>
                  <div class="text-[12px] mt-2">在测试单元层面，对提取的要素进行正交组合，并生成组合结果。</div>
                </div>
              </template>
            </a-trigger>
          </div>
        </template>

        <template #phase3Title>
          <div class="flex items-center">
            <div class="text-center w-[18px] h-[18px] rounded-[9px] text-[12px] bg-slate-300 mr-2">3</div>
            <span class="mr-2">案例生成</span>

            <a-trigger position="top" auto-fit-position :unmount-on-close="false">
              <icon-question-circle />
              <template #content>
                <div class="demo-basic">
                  <h4 class="font-bold text-[14px]">案例生成</h4>
                  <div class="text-[12px] mt-2">基于要素组合矩阵，自动化生成最终可执行的测试用例。</div>
                </div>
              </template>
            </a-trigger>
          </div>
        </template>

        <!-- 序号列 -->
        <template #index="{ rowIndex }">
          {{ formatIndex(rowIndex) }}
        </template>
        <!-- 序号列 -->
        <template #name="{ record }">
          {{ record.name }}
        </template>

        <!-- 要素提取列 -->
        <template #phase1="{ record }">
          <div class="flex items-center">
            <!-- <div class="w-[30px] h-[30px] mr-4">
              <a-progress
                :stroke-width="3"
                :stroke-color="getProgressColor(record.phases.phase1.status)"
                size="small"
                type="circle"
                :percent="record.phases.phase1.status === 'succeeded' ? 1 : Math.random() * 0.8 + 0.1"
              >
                <template #text>
                  <span class="inline-block w-6">{{ record.progress }}</span>
                </template>
              </a-progress>
            </div> -->
            <a-space class="text-primary cursor-pointer">
              <a-tooltip :content="record.phases.phase1.error" placement="top">
                <icon-info-circle v-if="record.phases.phase1.error" style="color: #fa5f4b" />
              </a-tooltip>

              <a-tag v-if="record.phases.phase1.status === 'not_started'" class="text-gray-500">未开始</a-tag>
              <span
                v-if="record.phases.phase1.status === 'succeeded'"
                class="mr-2"
                @click="handleExportElements(record)"
              >
                导出
              </span>
              <a-trigger position="top" auto-fit-position :unmount-on-close="false">
                <span
                  v-if="['succeeded', 'failed', 'partially_succeeded'].includes(record.phases.phase1.status)"
                  class="mr-2"
                  @click="handleRegenerateElements(record)"
                >
                  重新生成
                </span>
                <template #content>
                  <div class="demo-basic">
                    <h4 class="font-bold text-[14px]">要素提取</h4>
                    <div class="flex justify-between text-[12px] mt-4 mb-4">
                      <span>开始时间：</span>
                      <span>{{ formatDateTime(record.phases.phase1.start_time) }}</span>
                    </div>
                    <div class="flex justify-between text-[12px]">
                      <span>结束时间：</span>
                      <span class="clolor-gray">{{ formatDateTime(record.phases.phase1.end_time) }}</span>
                    </div>
                  </div>
                </template>
              </a-trigger>
            </a-space>
          </div>
        </template>

        <!-- 正交结合列 -->
        <template #phase2="{ record }">
          <div class="flex items-center">
            <!-- <div v-if="record.phases.phase2.status !== 'not_started'" class="w-[30px] h-[30px] mr-4">
              <a-progress
                :stroke-width="3"
                :stroke-color="getProgressColor(record.phases.phase1.status)"
                size="small"
                type="circle"
                :percent="record.phases.phase1.status === 'succeeded' ? 1 : Math.random() * 0.8 + 0.1"
              >
                <template #text>
                  <span class="inline-block w-6">
                    {{ record.phases.phase1.status === 'succeeded' ? 1 : Math.floor(Math.random() * 9) + 1 }}
                  </span>
                </template>
              </a-progress>
            </div> -->
            <a-space class="text-primary cursor-pointer">
              <a-tooltip :content="record.phases.phase2.error" placement="top">
                <icon-info-circle v-if="record.phases.phase2.error" style="color: #fa5f4b" />
              </a-tooltip>
              <a-tag v-if="record.phases.phase2.status === 'not_started'" class="text-gray-500">未开始</a-tag>
              <a-upload
                v-if="
                  record.phases.phase2.status === 'not_started' ||
                  record.phases.phase2.status === 'succeeded' ||
                  record.phases.phase2.status === 'partially_succeeded' ||
                  record.phases.phase2.status === 'failed'
                "
                :action="null"
                :auto-upload="false"
                :show-file-list="false"
                :multiple="false"
                accept=".xlsx,.xls"
                @change="(file) => handleOrthogonalUpload(record.id, file)"
              >
                <template #upload-button>
                  <span>
                    {{
                      ['succeeded', 'partially_succeeded', 'failed'].includes(record.phases.phase2.status)
                        ? '重新导入生成'
                        : '导入'
                    }}
                  </span>
                </template>
              </a-upload>

              <span
                v-if="record.phases.phase2.status === 'succeeded'"
                class="mr-2"
                @click="handleExportOrthogonal(record)"
              >
                导出
              </span>
              <a-trigger position="top" auto-fit-position :unmount-on-close="false">
                <span
                  v-if="['succeeded', 'failed', 'partially_succeeded'].includes(record.phases.phase2.status)"
                  class="mr-2"
                  @click="handleRegenerate(record)"
                >
                  重新生成
                </span>
                <template #content>
                  <div class="demo-basic">
                    <h4 class="font-bold text-[14px]">正交结合</h4>
                    <div class="flex justify-between text-[12px] mt-4 mb-4">
                      <span>开始时间：</span>
                      <span>{{ formatDateTime(record.phases.phase2.start_time) }}</span>
                    </div>
                    <div class="flex justify-between text-[12px]">
                      <span>结束时间：</span>
                      <span class="clolor-gray">{{ formatDateTime(record.phases.phase2.end_time) }}</span>
                    </div>
                  </div>
                </template>
              </a-trigger>
            </a-space>
          </div>
        </template>

        <!-- 案例生成列 -->
        <template #phase3="{ record }">
          <div class="flex items-center">
            <!-- <div v-if="record.phases.phase3.status !== 'not_started'" class="w-[30px] h-[30px] mr-4">
              <a-progress
                :stroke-width="3"
                :stroke-color="getProgressColor(record.phases.phase2.status)"
                size="small"
                type="circle"
                :percent="record.phases.phase3.status === 'succeeded' ? 1 : Math.random() * 0.8 + 0.1"
              >
                <template #text>
                  <span class="inline-block w-6">
                    {{ record.phases.phase3.status === 'succeeded' ? 1 : Math.floor(Math.random() * 9) + 1 }}
                  </span>
                </template>
              </a-progress>
            </div> -->
            <a-space class="text-primary cursor-pointer">
              <a-tooltip :content="record.phases.phase3.error" placement="top">
                <icon-info-circle v-if="record.phases.phase3.error" style="color: #fa5f4b" />
              </a-tooltip>

              <a-tag v-if="record.phases.phase3.status === 'not_started'" class="text-gray-500">未开始</a-tag>
              <a-upload
                v-if="
                  !record.phases.phase3.status ||
                  record.phases.phase3.status === 'not_started' ||
                  record.phases.phase3.status === 'failed' ||
                  record.phases.phase3.status === 'succeeded' ||
                  record.phases.phase3.status === 'partially_succeeded'
                "
                :action="null"
                :auto-upload="false"
                :show-file-list="false"
                :multiple="false"
                accept=".xlsx,.xls"
                @change="(file) => handleCaseUpload(record.id, file)"
              >
                <template #upload-button>
                  <span>
                    {{
                      ['succeeded', 'partially_succeeded'].includes(record.phases.phase3.status)
                        ? '重新导入生成'
                        : '导入'
                    }}
                  </span>
                </template>
              </a-upload>
              <span v-if="record.phases.phase3.status === 'succeeded'" class="mr-2" @click="handleExportCase(record)">
                导出
              </span>
              <a-trigger position="top" auto-fit-position :unmount-on-close="false">
                <span
                  v-if="['succeeded', 'failed', 'partially_succeeded'].includes(record.phases.phase3.status)"
                  class="mr-2"
                  @click="handleRegenerate(record)"
                >
                  重新生成
                </span>
                <template #content>
                  <div class="demo-basic">
                    <h4 class="font-bold text-[14px]">案例生成</h4>
                    <div class="flex justify-between text-[12px] mt-4 mb-4">
                      <span>开始时间：</span>
                      <span>{{ formatDateTime(record.phases.phase3.start_time) }}</span>
                    </div>
                    <div class="flex justify-between text-[12px]">
                      <span>结束时间：</span>
                      <span class="clolor-gray">{{ formatDateTime(record.phases.phase3.end_time) }}</span>
                    </div>
                  </div>
                </template>
              </a-trigger>
            </a-space>
          </div>
        </template>

        <!-- 会话创建时间列 -->
        <template #createTime="{ record }">
          <span>{{ formatDateTime(record.createTime || record.created_at || record.executeTime) }}</span>
        </template>

        <!-- 操作列 -->
        <template #operations="{ record }">
          <div class="text-primary cursor-pointer">
            <span class="mr-2" @click="handleDetail(record)">详情</span>
            <span class="mr-2" @click="handleTerminate(record)">终止</span>
            <span @click="handleDelete(record)">删除</span>
          </div>
        </template>
      </AiTable>
    </div>

    <!-- 创建助手模态窗口 -->
    <CreateAssistantModal v-model:visible="createModalVisible" @submit="handleCreateSuccess" />

    <!-- 创建会话窗口 -->
    <CreateChatModal
      v-model:visible="createChatModalVisible"
      title="新建会话"
      mode="create"
      @submit="handleCreateSuccess"
    />

    <!-- 重新生成要素弹窗 -->
    <CreateChatModal
      v-model:visible="regenerateElementsDialogVisible"
      title="重新生成要素"
      mode="regenerate"
      :initial-data="regenerateElementsInitialData"
      @submit="handleRegenerateElementsSuccess"
    />

    <!-- 重新生成正交组合弹窗 -->
    <RegenerateOrthogonalModal
      v-model:visible="regenerateDialogVisible"
      :task-id="currentTaskId"
      :categories="availableCategories"
      :error-categories="currentErrorCategories"
      @success="handleRegenerateSuccess"
    />
  </AiPageLayout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import AssistantCarousel from './components/AssistantCarousel.vue'
import CreateAssistantModal from './components/CreateAssistantModal.vue'
import CreateChatModal from './components/CreateChatModal.vue'
import RegenerateOrthogonalModal from './components/RegenerateOrthogonalModal.vue'
import {
  getAssistantList,
  getTaskList,
  exportElements,
  exportOrthogonal,
  exportCase,
  uploadOrthogonal,
  generateCase,
  terminateTask,
  deleteTask,
  regenerateElements,
  type TaskItem,
  type QueryParams
} from '@/apis/ai-assistant'
import { useTable } from '@/hooks'
import { formatDateTime } from '@/utils'
import { downloadBlob } from '@/utils/downloadFile'

// 类型定义已移至 @/apis/ai-assistant/type.ts
const assistantList = [
  {
    id: '1',
    name: '测试分析助手',
    description: '自动生成测试用例',
    icon: 'assistant-test.png',
    updateTime: '2025.05.14',
    count: '1,436',
    version: 'V1.8',
    type: '测试类'
  },
  {
    id: '2',
    name: '智能问答助手',
    description: '智能回答问题',
    icon: 'assistant-qa.png',
    updateTime: '2025.05.14',
    count: '1,438',
    version: 'V1.2',
    type: '问答类'
  },
  {
    id: '3',
    name: '代码审查助手',
    description: '代码质量分析',
    icon: 'assistant-code.png',
    updateTime: '2025.05.14',
    count: '1,764',
    version: 'V1.3',
    type: '测试类'
  },
  {
    id: '4',
    name: '文章写作助手',
    description: '智能生成文章',
    icon: 'assistant-writer.png',
    updateTime: '2025.05.14',
    count: '1,643',
    version: 'V2.4',
    type: '测试类'
  }
]
// 查询参数
const queryParams = reactive<QueryParams>({
  name: '',
  start_time: '',
  end_time: ''
})

// 助手列表数据
// const assistantList = ref<Assistant[]>([])

// 时间范围
const timeRange = ref<[string, string] | undefined>()

// 重新生成相关状态
const regenerateDialogVisible = ref(false)
const regenerateElementsDialogVisible = ref(false)
const currentTask = ref<TaskItem | null>(null)
const availableCategories = ref<string[]>([])
const currentTaskId = ref<string>('')
const currentErrorCategories = ref<string[]>([])

// 删除未使用的表单

// 删除未使用的表单

// 重新生成要素的初始数据
const regenerateElementsInitialData = ref({
  splitLevel: 0,
  sysNames: [] as string[],
  prompt: ''
})

// 系统选项 - 配置变量（在CreateChatModal中使用）
// const systemOptions = ['系统1', '系统2', '系统3', '核心系统', '业务系统', '数据系统']

// 表格列定义
const columns = [
  { title: '序号', slotName: 'index', width: 60 },
  { title: '会话名称', slotName: 'name', width: 180, ellipsis: true, tooltip: true },
  { title: '会话创建时间', slotName: 'createTime', width: 180 },
  {
    title: '要素提取',
    slotName: 'phase1',
    width: 300,
    titleSlotName: 'phase1Title'
  },
  {
    title: '正交结合',
    slotName: 'phase2',
    width: 300,
    titleSlotName: 'phase2Title'
  },
  {
    title: '案例生成',
    slotName: 'phase3',
    width: 300,
    titleSlotName: 'phase3Title'
  },
  { title: '操作', slotName: 'operations', width: 160, fixed: 'right' }
]
// 处理搜索参数
const buildSearchParams = () => {
  const params: QueryParams = {}

  if (queryParams.name) {
    params.name = queryParams.name
  }

  if (queryParams.start_time) {
    params.start_time = queryParams.start_time
  }

  if (queryParams.end_time) {
    params.end_time = queryParams.end_time
  }

  return params
}

const { tableData, loading, pagination, search, refresh } = useTable<TaskItem>(
  (page) => getTaskList({ ...buildSearchParams(), ...page }),
  { immediate: true, dataKey: 'items' }
)

// 格式化序号
const formatIndex = (index: number) => {
  return index < 9 ? `0${index + 1}` : `${index + 1}`
}
// 处理正交组合文件上传
const handleOrthogonalUpload = async (taskId: string, file: any) => {
  if (!file) return
  console.log(file[0])
  try {
    const formData = new FormData()
    formData.append('file', file[0]?.file)

    await uploadOrthogonal(taskId, formData)
    Message.success('正交组合文件上传成功')
    refresh()
  } catch (error) {
    console.log(error)
    console.error('正交组合文件上传失败', error)
    Message.error(error.response?.data?.detail)
  }
}

// 处理案例生成文件上传
const handleCaseUpload = async (taskId: string, file: any) => {
  if (!file) return
  try {
    await generateCase(taskId, file[0])
    Message.success('案例生成任务已创建')
    refresh()
  } catch (error) {
    console.error('案例生成失败', error)
    Message.error(error.response?.data?.detail)
  }
}
// 处理时间范围变化
const handleTimeRangeChange = (value: [string, string] | undefined) => {
  if (value && value.length === 2) {
    queryParams.start_time = value[0]
    queryParams.end_time = value[1]
  } else {
    queryParams.start_time = ''
    queryParams.end_time = ''
  }
}

// 获取评测值
const getAssessmentValue = (record: TaskItem) => {
  if (record.assessment) {
    return record.assessment
  }
  if (record.evaluation && record.evaluation !== '未评价') {
    return record.evaluation.replace(/[^0-9]/g, '') || '1'
  }
  return '1'
}

// 处理表格变化
const handleTableChange = (data: any) => {
  if (data.pagination) {
    pagination.current = data.pagination.current
    pagination.pageSize = data.pagination.pageSize
    refresh()
  }
}

// 处理搜索
const handleSearch = () => {
  search()
}

// 重置搜索
const resetSearch = () => {
  queryParams.name = ''
  queryParams.start_time = ''
  queryParams.end_time = ''
  timeRange.value = undefined
  search()
}

// 创建助手模态窗口控制
const createModalVisible = ref(false)

// 处理创建任务
const handleCreate = () => {
  createModalVisible.value = true
}

// 处理创建助手成功
const handleCreateSuccess = () => {
  fetchData()
  refresh() // 刷新任务列表
}

// 处理详情查看
const handleDetail = (record: TaskItem) => {
  Message.info(`查看详情: ${record.id}`)
}

// 处理终止任务
const handleTerminate = async (record: TaskItem) => {
  try {
    await terminateTask(record.id)
    Message.success('任务已终止')
    refresh()
  } catch (error) {
    console.error('终止任务失败', error)
    Message.error('终止任务失败')
  }
}

// 处理案例生成文件变更（用于重新导入生成）
// const handleFileChange = async (file: any, record: TaskItem) => {
//   if (!file || !record) return

//   try {
//     const uploadFile = file.file || file
//     await generateCase(record.id, uploadFile)
//     Message.success('案例生成任务已创建')
//     refresh()
//   } catch (error) {
//     console.error('生成案例失败', error)
//     Message.error('生成案例失败')
//   }
// }

// 处理删除任务
const handleDelete = async (record: TaskItem) => {
  try {
    await deleteTask(record.id)
    Message.success('任务已删除')
    refresh()
  } catch (error) {
    console.error('删除任务失败', error)
    Message.error('删除任务失败')
  }
}

// 处理导出 - 要素提取
const handleExportElements = async (record: TaskItem) => {
  try {
    const response = await exportElements(record.id)
    // 确保响应数据是 Blob 类型
    const blob = response.data instanceof Blob ? response.data : new Blob([response.data])
    await downloadBlob(blob, `${record.name || record.id}_elements_result.xlsx`)
    Message.success('导出成功')
  } catch (error) {
    console.error('导出要素提取结果失败', error)
    Message.error('导出要素提取结果失败')
  }
}

// 处理导出 - 正交组合
const handleExportOrthogonal = async (record: TaskItem) => {
  try {
    const response = await exportOrthogonal(record.id)
    // 确保响应数据是 Blob 类型
    const blob = response.data instanceof Blob ? response.data : new Blob([response.data])
    await downloadBlob(blob, `${record.name || record.id}_orthogonal_result.xlsx`)
    Message.success('导出成功')
  } catch (error) {
    console.error('导出正交组合结果失败', error)
    Message.error('导出正交组合结果失败')
  }
}

// 处理导出 - 案例生成
const handleExportCase = async (record: TaskItem) => {
  try {
    const response = await exportCase(record.id)
    // 确保响应数据是 Blob 类型
    const blob = response.data instanceof Blob ? response.data : new Blob([response.data])
    await downloadBlob(blob, `${record.name || record.id}_case_result.xlsx`)
    Message.success('导出成功')
  } catch (error) {
    console.error('导出案例生成结果失败', error)
    Message.error('导出案例生成结果失败')
  }
}

// 处理重新生成 - 要素提取
const handleRegenerateElements = (record: TaskItem) => {
  currentTask.value = record

  // 设置初始数据
  const splitLevel = record.split_level || 0
  let sysNames: string[] = []

  // 解析系统名称
  try {
    if (typeof record.sys_names === 'string') {
      sysNames = record.sys_names.split(',').map((name) => name.trim())
    } else if (Array.isArray(record.sys_names)) {
      sysNames = record.sys_names
    }
  } catch (error) {
    console.error('解析系统名称失败:', error)
    sysNames = []
  }

  const prompt = record.phases?.phase1?.prompt || ''

  // 更新初始数据
  regenerateElementsInitialData.value = {
    splitLevel,
    sysNames,
    prompt
  }

  regenerateElementsDialogVisible.value = true
}

// 处理重新生成要素成功
const handleRegenerateElementsSuccess = async (formData: any) => {
  if (!currentTask.value) return

  try {
    const data = new FormData()

    if (formData.split_level !== currentTask.value.split_level) {
      data.append('split_level', formData.split_level.toString())
    }

    if (JSON.stringify(formData.sys_names) !== JSON.stringify(currentTask.value.sys_names)) {
      data.append('sys_names', JSON.stringify(formData.sys_names))
    }

    if (formData.prompt !== currentTask.value.phases?.phase1?.prompt) {
      data.append('prompt', formData.prompt)
    }

    await regenerateElements(currentTask.value.id, data)
    Message.success('重新生成任务已创建')
    regenerateElementsDialogVisible.value = false
    refresh()
  } catch (error) {
    console.error('重新生成要素失败', error)
    Message.error('重新生成要素失败')
  }
}

// 处理重新生成 - 正交组合
const handleRegenerate = (record: TaskItem) => {
  currentTask.value = record
  currentTaskId.value = record.id
  availableCategories.value = record.categories || []
  currentErrorCategories.value = record.phases?.phase2?.error_categories || []
  regenerateDialogVisible.value = true
}

// 处理重新生成成功
const handleRegenerateSuccess = () => {
  refresh()
}

// 删除未使用的函数

// 删除未使用的函数

const createChatModalVisible = ref(false)
// 处理重新导入生成
const handleReimport = () => {
  createChatModalVisible.value = true
}

// 获取助手列表数据
// const fetchAssistantData = async () => {
//   try {
//     const res = await getAssistantList()
//     if (res && res.data) {
//       assistantList.value = res.data
//     }
//   } catch (error) {
//     console.error('获取助手列表失败', error)
//     Message.error('获取助手列表失败')
//   }
// }

// 获取所有数据
const fetchData = () => {
  // fetchAssistantData()
  // refresh()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
  refresh()
})
</script>

<style scoped>
.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-primary {
  color: #5147ff;
  cursor: pointer;
}

.assessment-circle {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  /* background-color: #5147ff; */
  color: white;
  font-size: 12px;
}

/* 调整进度条组件的样式 */
:deep(.arco-progress-circle-wrapper) {
  width: 35px !important;
  height: 35px !important;
}
:deep(.arco-progress-circle-text) {
  font-size: 12px;
}

:deep(.arco-progress-circle) {
  transform: scale(0.9);
}

.status-tag {
  background-color: #f4f4f6;
  color: #999999;
  border: none;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

/* 覆盖表格样式 */
:deep(.arco-table-th) {
  background-color: #f5f7fa !important;
  font-weight: normal !important;
  color: rgba(0, 0, 0, 0.85) !important;
}

:deep(.arco-table-td) {
  border-bottom: 1px solid #e8e8e8 !important;
}
.demo-basic {
  padding: 20px;
  min-width: 200px;
  width: auto;
  background-color: var(--color-bg-popup);
  border-radius: 4px;
  box-shadow: 0 2px 8px #00000026;
}
</style>
